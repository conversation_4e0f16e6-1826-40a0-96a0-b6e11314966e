# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-15 02:41:40
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 2071026239
:do {
    /tool user-manager user add customer="admin" username="2071026239" password="46430284" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071026239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071026239";
};

# المستخدم 2: 2032338339
:do {
    /tool user-manager user add customer="admin" username="2032338339" password="61632032" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032338339";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032338339";
};

# المستخدم 3: 2028903754
:do {
    /tool user-manager user add customer="admin" username="2028903754" password="08205936" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028903754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028903754";
};

# المستخدم 4: 2097447092
:do {
    /tool user-manager user add customer="admin" username="2097447092" password="52455563" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097447092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097447092";
};

# المستخدم 5: 2068297491
:do {
    /tool user-manager user add customer="admin" username="2068297491" password="64256283" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068297491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068297491";
};

# المستخدم 6: 2090353944
:do {
    /tool user-manager user add customer="admin" username="2090353944" password="06805349" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090353944";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090353944";
};

# المستخدم 7: 2095429824
:do {
    /tool user-manager user add customer="admin" username="2095429824" password="18993351" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095429824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095429824";
};

# المستخدم 8: 2056887988
:do {
    /tool user-manager user add customer="admin" username="2056887988" password="77030582" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056887988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056887988";
};

# المستخدم 9: 2069972785
:do {
    /tool user-manager user add customer="admin" username="2069972785" password="23991473" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069972785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069972785";
};

# المستخدم 10: 2005758627
:do {
    /tool user-manager user add customer="admin" username="2005758627" password="49799306" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005758627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005758627";
};

# المستخدم 11: 2092543500
:do {
    /tool user-manager user add customer="admin" username="2092543500" password="15319455" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092543500";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092543500";
};

# المستخدم 12: 2089678118
:do {
    /tool user-manager user add customer="admin" username="2089678118" password="52941854" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089678118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089678118";
};

# المستخدم 13: 2041237474
:do {
    /tool user-manager user add customer="admin" username="2041237474" password="78071108" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041237474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041237474";
};

# المستخدم 14: 2056581334
:do {
    /tool user-manager user add customer="admin" username="2056581334" password="20657575" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056581334";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056581334";
};

# المستخدم 15: 2075670707
:do {
    /tool user-manager user add customer="admin" username="2075670707" password="00981878" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075670707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075670707";
};

# المستخدم 16: 2089700792
:do {
    /tool user-manager user add customer="admin" username="2089700792" password="86097682" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089700792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089700792";
};

# المستخدم 17: 2067867145
:do {
    /tool user-manager user add customer="admin" username="2067867145" password="90063255" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067867145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067867145";
};

# المستخدم 18: 2037498709
:do {
    /tool user-manager user add customer="admin" username="2037498709" password="24480859" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037498709";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037498709";
};

# المستخدم 19: 2037676055
:do {
    /tool user-manager user add customer="admin" username="2037676055" password="87173204" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037676055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037676055";
};

# المستخدم 20: 2071800845
:do {
    /tool user-manager user add customer="admin" username="2071800845" password="77294614" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071800845";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071800845";
};

# المستخدم 21: 2066024557
:do {
    /tool user-manager user add customer="admin" username="2066024557" password="61355374" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066024557";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066024557";
};

# المستخدم 22: 2023766464
:do {
    /tool user-manager user add customer="admin" username="2023766464" password="39667006" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023766464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023766464";
};

# المستخدم 23: 2054628258
:do {
    /tool user-manager user add customer="admin" username="2054628258" password="70403412" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054628258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054628258";
};

# المستخدم 24: 2059746970
:do {
    /tool user-manager user add customer="admin" username="2059746970" password="90560448" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059746970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059746970";
};

# المستخدم 25: 2091100470
:do {
    /tool user-manager user add customer="admin" username="2091100470" password="02686009" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091100470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091100470";
};

# المستخدم 26: 2070724216
:do {
    /tool user-manager user add customer="admin" username="2070724216" password="07036184" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070724216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070724216";
};

# المستخدم 27: 2083743792
:do {
    /tool user-manager user add customer="admin" username="2083743792" password="99218462" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083743792";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083743792";
};

# المستخدم 28: 2076018435
:do {
    /tool user-manager user add customer="admin" username="2076018435" password="70830323" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076018435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076018435";
};

# المستخدم 29: 2062807826
:do {
    /tool user-manager user add customer="admin" username="2062807826" password="95347515" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062807826";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062807826";
};

# المستخدم 30: 2049988142
:do {
    /tool user-manager user add customer="admin" username="2049988142" password="06783640" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049988142";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049988142";
};

# المستخدم 31: 2053064808
:do {
    /tool user-manager user add customer="admin" username="2053064808" password="02835219" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053064808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053064808";
};

# المستخدم 32: 2061796133
:do {
    /tool user-manager user add customer="admin" username="2061796133" password="47787886" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061796133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061796133";
};

# المستخدم 33: 2021140953
:do {
    /tool user-manager user add customer="admin" username="2021140953" password="91319123" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021140953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021140953";
};

# المستخدم 34: 2028112349
:do {
    /tool user-manager user add customer="admin" username="2028112349" password="54803797" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028112349";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028112349";
};

# المستخدم 35: 2000808365
:do {
    /tool user-manager user add customer="admin" username="2000808365" password="07358039" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000808365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000808365";
};

# المستخدم 36: 2027868728
:do {
    /tool user-manager user add customer="admin" username="2027868728" password="14016790" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027868728";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027868728";
};

# المستخدم 37: 2000384538
:do {
    /tool user-manager user add customer="admin" username="2000384538" password="35465424" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000384538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000384538";
};

# المستخدم 38: 2098043831
:do {
    /tool user-manager user add customer="admin" username="2098043831" password="26214190" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098043831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098043831";
};

# المستخدم 39: 2094214307
:do {
    /tool user-manager user add customer="admin" username="2094214307" password="02434419" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094214307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094214307";
};

# المستخدم 40: 2057503427
:do {
    /tool user-manager user add customer="admin" username="2057503427" password="24783949" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057503427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057503427";
};

# المستخدم 41: 2098745207
:do {
    /tool user-manager user add customer="admin" username="2098745207" password="73337581" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098745207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098745207";
};

# المستخدم 42: 2057020650
:do {
    /tool user-manager user add customer="admin" username="2057020650" password="45778184" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057020650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057020650";
};

# المستخدم 43: 2051920117
:do {
    /tool user-manager user add customer="admin" username="2051920117" password="86980671" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051920117";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051920117";
};

# المستخدم 44: 2071069774
:do {
    /tool user-manager user add customer="admin" username="2071069774" password="60206952" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071069774";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071069774";
};

# المستخدم 45: 2029956215
:do {
    /tool user-manager user add customer="admin" username="2029956215" password="05081940" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029956215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029956215";
};

# المستخدم 46: 2057974805
:do {
    /tool user-manager user add customer="admin" username="2057974805" password="29485293" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057974805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057974805";
};

# المستخدم 47: 2007962580
:do {
    /tool user-manager user add customer="admin" username="2007962580" password="08703876" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007962580";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007962580";
};

# المستخدم 48: 2035677828
:do {
    /tool user-manager user add customer="admin" username="2035677828" password="35596810" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035677828";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035677828";
};

# المستخدم 49: 2076373008
:do {
    /tool user-manager user add customer="admin" username="2076373008" password="31791564" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076373008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076373008";
};

# المستخدم 50: 2067145011
:do {
    /tool user-manager user add customer="admin" username="2067145011" password="23422328" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067145011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067145011";
};

# المستخدم 51: 2010916662
:do {
    /tool user-manager user add customer="admin" username="2010916662" password="55212895" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010916662";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010916662";
};

# المستخدم 52: 2029774749
:do {
    /tool user-manager user add customer="admin" username="2029774749" password="91326845" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029774749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029774749";
};

# المستخدم 53: 2083500566
:do {
    /tool user-manager user add customer="admin" username="2083500566" password="26649314" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083500566";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083500566";
};

# المستخدم 54: 2030858299
:do {
    /tool user-manager user add customer="admin" username="2030858299" password="15248315" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030858299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030858299";
};

# المستخدم 55: 2017802431
:do {
    /tool user-manager user add customer="admin" username="2017802431" password="93970355" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017802431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017802431";
};

# المستخدم 56: 2008193437
:do {
    /tool user-manager user add customer="admin" username="2008193437" password="38708883" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008193437";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008193437";
};

# المستخدم 57: 2056947626
:do {
    /tool user-manager user add customer="admin" username="2056947626" password="01149506" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056947626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056947626";
};

# المستخدم 58: 2012769405
:do {
    /tool user-manager user add customer="admin" username="2012769405" password="01192612" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012769405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012769405";
};

# المستخدم 59: 2095062220
:do {
    /tool user-manager user add customer="admin" username="2095062220" password="09389737" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095062220";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095062220";
};

# المستخدم 60: 2081963856
:do {
    /tool user-manager user add customer="admin" username="2081963856" password="05629057" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081963856";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081963856";
};

# المستخدم 61: 2025909638
:do {
    /tool user-manager user add customer="admin" username="2025909638" password="05340000" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025909638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025909638";
};

# المستخدم 62: 2037706932
:do {
    /tool user-manager user add customer="admin" username="2037706932" password="24691893" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037706932";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037706932";
};

# المستخدم 63: 2066019159
:do {
    /tool user-manager user add customer="admin" username="2066019159" password="62621876" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066019159";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066019159";
};

# المستخدم 64: 2028399434
:do {
    /tool user-manager user add customer="admin" username="2028399434" password="57071093" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028399434";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028399434";
};

# المستخدم 65: 2017565699
:do {
    /tool user-manager user add customer="admin" username="2017565699" password="83511766" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017565699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017565699";
};

# المستخدم 66: 2036220687
:do {
    /tool user-manager user add customer="admin" username="2036220687" password="15250009" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036220687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036220687";
};

# المستخدم 67: 2040540451
:do {
    /tool user-manager user add customer="admin" username="2040540451" password="06164095" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040540451";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040540451";
};

# المستخدم 68: 2014352804
:do {
    /tool user-manager user add customer="admin" username="2014352804" password="20360986" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014352804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014352804";
};

# المستخدم 69: 2088405374
:do {
    /tool user-manager user add customer="admin" username="2088405374" password="73119668" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088405374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088405374";
};

# المستخدم 70: 2042405645
:do {
    /tool user-manager user add customer="admin" username="2042405645" password="71829077" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042405645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042405645";
};

# المستخدم 71: 2044877348
:do {
    /tool user-manager user add customer="admin" username="2044877348" password="98258772" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044877348";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044877348";
};

# المستخدم 72: 2082522295
:do {
    /tool user-manager user add customer="admin" username="2082522295" password="97535278" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082522295";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082522295";
};

# المستخدم 73: 2087972128
:do {
    /tool user-manager user add customer="admin" username="2087972128" password="16803950" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087972128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087972128";
};

# المستخدم 74: 2016916210
:do {
    /tool user-manager user add customer="admin" username="2016916210" password="57426145" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016916210";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016916210";
};

# المستخدم 75: 2089888314
:do {
    /tool user-manager user add customer="admin" username="2089888314" password="22275725" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089888314";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089888314";
};

# المستخدم 76: 2005515189
:do {
    /tool user-manager user add customer="admin" username="2005515189" password="44936834" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005515189";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005515189";
};

# المستخدم 77: 2052684187
:do {
    /tool user-manager user add customer="admin" username="2052684187" password="14138558" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052684187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052684187";
};

# المستخدم 78: 2098564482
:do {
    /tool user-manager user add customer="admin" username="2098564482" password="37089209" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098564482";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098564482";
};

# المستخدم 79: 2005327623
:do {
    /tool user-manager user add customer="admin" username="2005327623" password="75822607" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005327623";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005327623";
};

# المستخدم 80: 2024860286
:do {
    /tool user-manager user add customer="admin" username="2024860286" password="92516878" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024860286";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024860286";
};

# المستخدم 81: 2037392825
:do {
    /tool user-manager user add customer="admin" username="2037392825" password="60325712" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037392825";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037392825";
};

# المستخدم 82: 2081350257
:do {
    /tool user-manager user add customer="admin" username="2081350257" password="82170111" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081350257";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081350257";
};

# المستخدم 83: 2014332281
:do {
    /tool user-manager user add customer="admin" username="2014332281" password="45615640" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014332281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014332281";
};

# المستخدم 84: 2088186350
:do {
    /tool user-manager user add customer="admin" username="2088186350" password="06661920" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088186350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088186350";
};

# المستخدم 85: 2036891829
:do {
    /tool user-manager user add customer="admin" username="2036891829" password="69912322" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036891829";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036891829";
};

# المستخدم 86: 2027892241
:do {
    /tool user-manager user add customer="admin" username="2027892241" password="37033880" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027892241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027892241";
};

# المستخدم 87: 2017015626
:do {
    /tool user-manager user add customer="admin" username="2017015626" password="56637439" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017015626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017015626";
};

# المستخدم 88: 2043800591
:do {
    /tool user-manager user add customer="admin" username="2043800591" password="94214209" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043800591";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043800591";
};

# المستخدم 89: 2040974781
:do {
    /tool user-manager user add customer="admin" username="2040974781" password="96590782" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040974781";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040974781";
};

# المستخدم 90: 2050356722
:do {
    /tool user-manager user add customer="admin" username="2050356722" password="80779379" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050356722";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050356722";
};

# المستخدم 91: 2022494479
:do {
    /tool user-manager user add customer="admin" username="2022494479" password="45404406" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022494479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022494479";
};

# المستخدم 92: 2094200634
:do {
    /tool user-manager user add customer="admin" username="2094200634" password="58773859" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094200634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094200634";
};

# المستخدم 93: 2042099313
:do {
    /tool user-manager user add customer="admin" username="2042099313" password="11723612" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042099313";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042099313";
};

# المستخدم 94: 2036976385
:do {
    /tool user-manager user add customer="admin" username="2036976385" password="97963675" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036976385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036976385";
};

# المستخدم 95: 2094086350
:do {
    /tool user-manager user add customer="admin" username="2094086350" password="24832945" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094086350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094086350";
};

# المستخدم 96: 2077346658
:do {
    /tool user-manager user add customer="admin" username="2077346658" password="75984843" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077346658";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077346658";
};

# المستخدم 97: 2025977179
:do {
    /tool user-manager user add customer="admin" username="2025977179" password="76669293" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025977179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025977179";
};

# المستخدم 98: 2014388289
:do {
    /tool user-manager user add customer="admin" username="2014388289" password="85167943" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014388289";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014388289";
};

# المستخدم 99: 2040882891
:do {
    /tool user-manager user add customer="admin" username="2040882891" password="98982799" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040882891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040882891";
};

# المستخدم 100: 2065317782
:do {
    /tool user-manager user add customer="admin" username="2065317782" password="26980590" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065317782";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065317782";
};

# المستخدم 101: 2000045029
:do {
    /tool user-manager user add customer="admin" username="2000045029" password="18499851" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000045029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000045029";
};

# المستخدم 102: 2030367431
:do {
    /tool user-manager user add customer="admin" username="2030367431" password="90248559" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030367431";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030367431";
};

# المستخدم 103: 2057867172
:do {
    /tool user-manager user add customer="admin" username="2057867172" password="53612767" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057867172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057867172";
};

# المستخدم 104: 2098012819
:do {
    /tool user-manager user add customer="admin" username="2098012819" password="89022871" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098012819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098012819";
};

# المستخدم 105: 2035464408
:do {
    /tool user-manager user add customer="admin" username="2035464408" password="02647812" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035464408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035464408";
};

# المستخدم 106: 2000296282
:do {
    /tool user-manager user add customer="admin" username="2000296282" password="53559823" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000296282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000296282";
};

# المستخدم 107: 2010725018
:do {
    /tool user-manager user add customer="admin" username="2010725018" password="43392713" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010725018";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010725018";
};

# المستخدم 108: 2015149532
:do {
    /tool user-manager user add customer="admin" username="2015149532" password="20285518" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015149532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015149532";
};

# المستخدم 109: 2083819405
:do {
    /tool user-manager user add customer="admin" username="2083819405" password="27190022" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083819405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083819405";
};

# المستخدم 110: 2040998297
:do {
    /tool user-manager user add customer="admin" username="2040998297" password="89735463" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040998297";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040998297";
};

# المستخدم 111: 2086681050
:do {
    /tool user-manager user add customer="admin" username="2086681050" password="49416688" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086681050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086681050";
};

# المستخدم 112: 2076744536
:do {
    /tool user-manager user add customer="admin" username="2076744536" password="74963752" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076744536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076744536";
};

# المستخدم 113: 2028249865
:do {
    /tool user-manager user add customer="admin" username="2028249865" password="04190402" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028249865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028249865";
};

# المستخدم 114: 2054090478
:do {
    /tool user-manager user add customer="admin" username="2054090478" password="99244645" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054090478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054090478";
};

# المستخدم 115: 2019284352
:do {
    /tool user-manager user add customer="admin" username="2019284352" password="54070116" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019284352";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019284352";
};

# المستخدم 116: 2011036681
:do {
    /tool user-manager user add customer="admin" username="2011036681" password="67204745" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011036681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011036681";
};

# المستخدم 117: 2018075404
:do {
    /tool user-manager user add customer="admin" username="2018075404" password="37378896" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018075404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018075404";
};

# المستخدم 118: 2034725116
:do {
    /tool user-manager user add customer="admin" username="2034725116" password="45163313" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034725116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034725116";
};

# المستخدم 119: 2015220229
:do {
    /tool user-manager user add customer="admin" username="2015220229" password="58579784" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015220229";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015220229";
};

# المستخدم 120: 2001085926
:do {
    /tool user-manager user add customer="admin" username="2001085926" password="37866412" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001085926";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001085926";
};

# المستخدم 121: 2010078096
:do {
    /tool user-manager user add customer="admin" username="2010078096" password="64214702" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010078096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010078096";
};

# المستخدم 122: 2036922759
:do {
    /tool user-manager user add customer="admin" username="2036922759" password="20867780" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036922759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036922759";
};

# المستخدم 123: 2051953957
:do {
    /tool user-manager user add customer="admin" username="2051953957" password="43573434" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051953957";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051953957";
};

# المستخدم 124: 2074056242
:do {
    /tool user-manager user add customer="admin" username="2074056242" password="36333426" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074056242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074056242";
};

# المستخدم 125: 2036360085
:do {
    /tool user-manager user add customer="admin" username="2036360085" password="50257113" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036360085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036360085";
};

# المستخدم 126: 2042100714
:do {
    /tool user-manager user add customer="admin" username="2042100714" password="19462596" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042100714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042100714";
};

# المستخدم 127: 2008339000
:do {
    /tool user-manager user add customer="admin" username="2008339000" password="93836105" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008339000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008339000";
};

# المستخدم 128: 2063775665
:do {
    /tool user-manager user add customer="admin" username="2063775665" password="14166135" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063775665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063775665";
};

# المستخدم 129: 2079925108
:do {
    /tool user-manager user add customer="admin" username="2079925108" password="22154415" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079925108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079925108";
};

# المستخدم 130: 2089675714
:do {
    /tool user-manager user add customer="admin" username="2089675714" password="90223260" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089675714";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089675714";
};

# المستخدم 131: 2030949176
:do {
    /tool user-manager user add customer="admin" username="2030949176" password="71427574" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030949176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030949176";
};

# المستخدم 132: 2084661952
:do {
    /tool user-manager user add customer="admin" username="2084661952" password="29076061" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084661952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084661952";
};

# المستخدم 133: 2019499214
:do {
    /tool user-manager user add customer="admin" username="2019499214" password="44820471" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019499214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019499214";
};

# المستخدم 134: 2000050331
:do {
    /tool user-manager user add customer="admin" username="2000050331" password="93162172" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000050331";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000050331";
};

# المستخدم 135: 2001056583
:do {
    /tool user-manager user add customer="admin" username="2001056583" password="71251205" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001056583";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001056583";
};

# المستخدم 136: 2034208798
:do {
    /tool user-manager user add customer="admin" username="2034208798" password="45619937" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034208798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034208798";
};

# المستخدم 137: 2063508602
:do {
    /tool user-manager user add customer="admin" username="2063508602" password="30958421" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063508602";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063508602";
};

# المستخدم 138: 2095525761
:do {
    /tool user-manager user add customer="admin" username="2095525761" password="96115181" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095525761";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095525761";
};

# المستخدم 139: 2003659350
:do {
    /tool user-manager user add customer="admin" username="2003659350" password="13725952" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003659350";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003659350";
};

# المستخدم 140: 2086978457
:do {
    /tool user-manager user add customer="admin" username="2086978457" password="66153973" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086978457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086978457";
};

# المستخدم 141: 2067348549
:do {
    /tool user-manager user add customer="admin" username="2067348549" password="27602503" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067348549";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067348549";
};

# المستخدم 142: 2004611785
:do {
    /tool user-manager user add customer="admin" username="2004611785" password="03961201" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004611785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004611785";
};

# المستخدم 143: 2024781060
:do {
    /tool user-manager user add customer="admin" username="2024781060" password="20791468" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024781060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024781060";
};

# المستخدم 144: 2050190952
:do {
    /tool user-manager user add customer="admin" username="2050190952" password="06210300" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050190952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050190952";
};

# المستخدم 145: 2013526203
:do {
    /tool user-manager user add customer="admin" username="2013526203" password="43897583" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013526203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013526203";
};

# المستخدم 146: 2090006089
:do {
    /tool user-manager user add customer="admin" username="2090006089" password="37674348" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090006089";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090006089";
};

# المستخدم 147: 2056929017
:do {
    /tool user-manager user add customer="admin" username="2056929017" password="05189163" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056929017";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056929017";
};

# المستخدم 148: 2003671806
:do {
    /tool user-manager user add customer="admin" username="2003671806" password="18662557" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003671806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003671806";
};

# المستخدم 149: 2005003293
:do {
    /tool user-manager user add customer="admin" username="2005003293" password="75952288" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005003293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005003293";
};

# المستخدم 150: 2071209393
:do {
    /tool user-manager user add customer="admin" username="2071209393" password="10705521" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071209393";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071209393";
};

# المستخدم 151: 2009912168
:do {
    /tool user-manager user add customer="admin" username="2009912168" password="06015104" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009912168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009912168";
};

# المستخدم 152: 2017597292
:do {
    /tool user-manager user add customer="admin" username="2017597292" password="99004963" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017597292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017597292";
};

# المستخدم 153: 2084862060
:do {
    /tool user-manager user add customer="admin" username="2084862060" password="94017181" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084862060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084862060";
};

# المستخدم 154: 2014275378
:do {
    /tool user-manager user add customer="admin" username="2014275378" password="03830469" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014275378";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014275378";
};

# المستخدم 155: 2037871135
:do {
    /tool user-manager user add customer="admin" username="2037871135" password="00979448" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037871135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037871135";
};

# المستخدم 156: 2002704546
:do {
    /tool user-manager user add customer="admin" username="2002704546" password="08210991" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002704546";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002704546";
};

# المستخدم 157: 2032964133
:do {
    /tool user-manager user add customer="admin" username="2032964133" password="45361733" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032964133";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032964133";
};

# المستخدم 158: 2008240070
:do {
    /tool user-manager user add customer="admin" username="2008240070" password="45468084" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008240070";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008240070";
};

# المستخدم 159: 2079042985
:do {
    /tool user-manager user add customer="admin" username="2079042985" password="27783591" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079042985";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079042985";
};

# المستخدم 160: 2080405606
:do {
    /tool user-manager user add customer="admin" username="2080405606" password="89224694" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080405606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080405606";
};

# المستخدم 161: 2002134329
:do {
    /tool user-manager user add customer="admin" username="2002134329" password="92417087" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002134329";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002134329";
};

# المستخدم 162: 2030972582
:do {
    /tool user-manager user add customer="admin" username="2030972582" password="16186947" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030972582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030972582";
};

# المستخدم 163: 2076908838
:do {
    /tool user-manager user add customer="admin" username="2076908838" password="93918249" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076908838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076908838";
};

# المستخدم 164: 2036526383
:do {
    /tool user-manager user add customer="admin" username="2036526383" password="50774195" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036526383";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036526383";
};

# المستخدم 165: 2073019908
:do {
    /tool user-manager user add customer="admin" username="2073019908" password="14275963" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073019908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073019908";
};

# المستخدم 166: 2008890340
:do {
    /tool user-manager user add customer="admin" username="2008890340" password="62010777" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008890340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008890340";
};

# المستخدم 167: 2030447924
:do {
    /tool user-manager user add customer="admin" username="2030447924" password="27133654" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2030447924";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2030447924";
};

# المستخدم 168: 2041418134
:do {
    /tool user-manager user add customer="admin" username="2041418134" password="54419599" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041418134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041418134";
};

# المستخدم 169: 2029638134
:do {
    /tool user-manager user add customer="admin" username="2029638134" password="68484375" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029638134";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029638134";
};

# المستخدم 170: 2037452556
:do {
    /tool user-manager user add customer="admin" username="2037452556" password="62824111" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037452556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037452556";
};

# المستخدم 171: 2093769333
:do {
    /tool user-manager user add customer="admin" username="2093769333" password="14418838" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093769333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093769333";
};

# المستخدم 172: 2047311279
:do {
    /tool user-manager user add customer="admin" username="2047311279" password="30451203" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047311279";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047311279";
};

# المستخدم 173: 2082018177
:do {
    /tool user-manager user add customer="admin" username="2082018177" password="55189390" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082018177";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082018177";
};

# المستخدم 174: 2071674235
:do {
    /tool user-manager user add customer="admin" username="2071674235" password="71854697" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071674235";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071674235";
};

# المستخدم 175: 2053344648
:do {
    /tool user-manager user add customer="admin" username="2053344648" password="92811717" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053344648";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053344648";
};

# المستخدم 176: 2002837097
:do {
    /tool user-manager user add customer="admin" username="2002837097" password="26331518" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002837097";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002837097";
};

# المستخدم 177: 2045569627
:do {
    /tool user-manager user add customer="admin" username="2045569627" password="26900261" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045569627";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045569627";
};

# المستخدم 178: 2091940652
:do {
    /tool user-manager user add customer="admin" username="2091940652" password="52524735" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091940652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091940652";
};

# المستخدم 179: 2020688104
:do {
    /tool user-manager user add customer="admin" username="2020688104" password="75361096" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020688104";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020688104";
};

# المستخدم 180: 2067302316
:do {
    /tool user-manager user add customer="admin" username="2067302316" password="99519972" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067302316";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067302316";
};

# المستخدم 181: 2002964525
:do {
    /tool user-manager user add customer="admin" username="2002964525" password="44472715" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002964525";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002964525";
};

# المستخدم 182: 2066960027
:do {
    /tool user-manager user add customer="admin" username="2066960027" password="70392407" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066960027";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066960027";
};

# المستخدم 183: 2004077642
:do {
    /tool user-manager user add customer="admin" username="2004077642" password="04134224" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2004077642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2004077642";
};

# المستخدم 184: 2001274253
:do {
    /tool user-manager user add customer="admin" username="2001274253" password="00685459" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001274253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001274253";
};

# المستخدم 185: 2097184241
:do {
    /tool user-manager user add customer="admin" username="2097184241" password="02037180" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097184241";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097184241";
};

# المستخدم 186: 2053977595
:do {
    /tool user-manager user add customer="admin" username="2053977595" password="25918804" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053977595";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053977595";
};

# المستخدم 187: 2045625357
:do {
    /tool user-manager user add customer="admin" username="2045625357" password="25042553" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2045625357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2045625357";
};

# المستخدم 188: 2038426093
:do {
    /tool user-manager user add customer="admin" username="2038426093" password="61794079" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038426093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038426093";
};

# المستخدم 189: 2044096601
:do {
    /tool user-manager user add customer="admin" username="2044096601" password="74597263" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044096601";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044096601";
};

# المستخدم 190: 2039886725
:do {
    /tool user-manager user add customer="admin" username="2039886725" password="40498666" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039886725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039886725";
};

# المستخدم 191: 2052014330
:do {
    /tool user-manager user add customer="admin" username="2052014330" password="77878601" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052014330";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052014330";
};

# المستخدم 192: 2083850182
:do {
    /tool user-manager user add customer="admin" username="2083850182" password="78825338" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083850182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083850182";
};

# المستخدم 193: 2014867800
:do {
    /tool user-manager user add customer="admin" username="2014867800" password="74912266" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014867800";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014867800";
};

# المستخدم 194: 2046899320
:do {
    /tool user-manager user add customer="admin" username="2046899320" password="03757125" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046899320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046899320";
};

# المستخدم 195: 2031044675
:do {
    /tool user-manager user add customer="admin" username="2031044675" password="11553405" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031044675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031044675";
};

# المستخدم 196: 2087256775
:do {
    /tool user-manager user add customer="admin" username="2087256775" password="89174646" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087256775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087256775";
};

# المستخدم 197: 2024932258
:do {
    /tool user-manager user add customer="admin" username="2024932258" password="27825135" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024932258";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024932258";
};

# المستخدم 198: 2009542805
:do {
    /tool user-manager user add customer="admin" username="2009542805" password="95340355" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009542805";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009542805";
};

# المستخدم 199: 2011474660
:do {
    /tool user-manager user add customer="admin" username="2011474660" password="24687600" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011474660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011474660";
};

# المستخدم 200: 2010934816
:do {
    /tool user-manager user add customer="admin" username="2010934816" password="03815077" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2010934816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2010934816";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
