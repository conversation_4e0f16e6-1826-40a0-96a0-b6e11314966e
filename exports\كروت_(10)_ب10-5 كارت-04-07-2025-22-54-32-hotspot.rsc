# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 22:54:32
# القالب: 10
# النظام: hotspot
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 5 مستخدم Hotspot...";

# المستخدم 1: 1018256328
:do {
    /ip hotspot user add name="1018256328" password="7478" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1018256328";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1018256328";
};

# المستخدم 2: 1029131059
:do {
    /ip hotspot user add name="1029131059" password="1947" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1029131059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1029131059";
};

# المستخدم 3: 1039167534
:do {
    /ip hotspot user add name="1039167534" password="7693" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1039167534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1039167534";
};

# المستخدم 4: 1026193293
:do {
    /ip hotspot user add name="1026193293" password="5353" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1026193293";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1026193293";
};

# المستخدم 5: 1034578267
:do {
    /ip hotspot user add name="1034578267" password="3619" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1034578267";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1034578267";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
