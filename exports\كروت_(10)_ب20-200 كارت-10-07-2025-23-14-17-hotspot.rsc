# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-10 23:14:18
# القالب: 10
# النظام: hotspot
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 200 مستخدم Hotspot...";

# المستخدم 1: 02223791585
:do {
    /ip hotspot user add name="02223791585" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02223791585";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02223791585";
};

# المستخدم 2: 02218035224
:do {
    /ip hotspot user add name="02218035224" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02218035224";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02218035224";
};

# المستخدم 3: 02543389077
:do {
    /ip hotspot user add name="02543389077" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02543389077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02543389077";
};

# المستخدم 4: 02380910323
:do {
    /ip hotspot user add name="02380910323" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02380910323";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02380910323";
};

# المستخدم 5: 02135952553
:do {
    /ip hotspot user add name="02135952553" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02135952553";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02135952553";
};

# المستخدم 6: 02249053720
:do {
    /ip hotspot user add name="02249053720" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02249053720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02249053720";
};

# المستخدم 7: 02298838906
:do {
    /ip hotspot user add name="02298838906" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02298838906";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02298838906";
};

# المستخدم 8: 02924066413
:do {
    /ip hotspot user add name="02924066413" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02924066413";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02924066413";
};

# المستخدم 9: 02204779594
:do {
    /ip hotspot user add name="02204779594" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02204779594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02204779594";
};

# المستخدم 10: 02810511970
:do {
    /ip hotspot user add name="02810511970" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02810511970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02810511970";
};

# المستخدم 11: 02554608822
:do {
    /ip hotspot user add name="02554608822" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02554608822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02554608822";
};

# المستخدم 12: 02118462086
:do {
    /ip hotspot user add name="02118462086" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02118462086";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02118462086";
};

# المستخدم 13: 02483995427
:do {
    /ip hotspot user add name="02483995427" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02483995427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02483995427";
};

# المستخدم 14: 02187474055
:do {
    /ip hotspot user add name="02187474055" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02187474055";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02187474055";
};

# المستخدم 15: 02016249112
:do {
    /ip hotspot user add name="02016249112" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02016249112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02016249112";
};

# المستخدم 16: 02465666635
:do {
    /ip hotspot user add name="02465666635" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02465666635";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02465666635";
};

# المستخدم 17: 02423565672
:do {
    /ip hotspot user add name="02423565672" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02423565672";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02423565672";
};

# المستخدم 18: 02914923587
:do {
    /ip hotspot user add name="02914923587" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02914923587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02914923587";
};

# المستخدم 19: 02028980406
:do {
    /ip hotspot user add name="02028980406" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02028980406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02028980406";
};

# المستخدم 20: 02073806953
:do {
    /ip hotspot user add name="02073806953" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02073806953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02073806953";
};

# المستخدم 21: 02212969452
:do {
    /ip hotspot user add name="02212969452" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02212969452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02212969452";
};

# المستخدم 22: 02954401651
:do {
    /ip hotspot user add name="02954401651" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02954401651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02954401651";
};

# المستخدم 23: 02939693528
:do {
    /ip hotspot user add name="02939693528" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02939693528";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02939693528";
};

# المستخدم 24: 02799308848
:do {
    /ip hotspot user add name="02799308848" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02799308848";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02799308848";
};

# المستخدم 25: 02971707347
:do {
    /ip hotspot user add name="02971707347" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02971707347";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02971707347";
};

# المستخدم 26: 02352863325
:do {
    /ip hotspot user add name="02352863325" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02352863325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02352863325";
};

# المستخدم 27: 02575779660
:do {
    /ip hotspot user add name="02575779660" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02575779660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02575779660";
};

# المستخدم 28: 02907123303
:do {
    /ip hotspot user add name="02907123303" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02907123303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02907123303";
};

# المستخدم 29: 02710701310
:do {
    /ip hotspot user add name="02710701310" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02710701310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02710701310";
};

# المستخدم 30: 02576246058
:do {
    /ip hotspot user add name="02576246058" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02576246058";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02576246058";
};

# المستخدم 31: 02642800203
:do {
    /ip hotspot user add name="02642800203" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02642800203";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02642800203";
};

# المستخدم 32: 02339121539
:do {
    /ip hotspot user add name="02339121539" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02339121539";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02339121539";
};

# المستخدم 33: 02198385144
:do {
    /ip hotspot user add name="02198385144" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02198385144";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02198385144";
};

# المستخدم 34: 02896649518
:do {
    /ip hotspot user add name="02896649518" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02896649518";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02896649518";
};

# المستخدم 35: 02732194980
:do {
    /ip hotspot user add name="02732194980" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02732194980";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02732194980";
};

# المستخدم 36: 02524975268
:do {
    /ip hotspot user add name="02524975268" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02524975268";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02524975268";
};

# المستخدم 37: 02344978629
:do {
    /ip hotspot user add name="02344978629" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02344978629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02344978629";
};

# المستخدم 38: 02452155586
:do {
    /ip hotspot user add name="02452155586" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02452155586";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02452155586";
};

# المستخدم 39: 02664764727
:do {
    /ip hotspot user add name="02664764727" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02664764727";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02664764727";
};

# المستخدم 40: 02903871056
:do {
    /ip hotspot user add name="02903871056" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02903871056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02903871056";
};

# المستخدم 41: 02967101255
:do {
    /ip hotspot user add name="02967101255" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02967101255";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02967101255";
};

# المستخدم 42: 02262808987
:do {
    /ip hotspot user add name="02262808987" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02262808987";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02262808987";
};

# المستخدم 43: 02075479020
:do {
    /ip hotspot user add name="02075479020" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02075479020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02075479020";
};

# المستخدم 44: 02567000694
:do {
    /ip hotspot user add name="02567000694" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02567000694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02567000694";
};

# المستخدم 45: 02190731179
:do {
    /ip hotspot user add name="02190731179" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02190731179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02190731179";
};

# المستخدم 46: 02424438418
:do {
    /ip hotspot user add name="02424438418" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02424438418";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02424438418";
};

# المستخدم 47: 02723052950
:do {
    /ip hotspot user add name="02723052950" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02723052950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02723052950";
};

# المستخدم 48: 02498794961
:do {
    /ip hotspot user add name="02498794961" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02498794961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02498794961";
};

# المستخدم 49: 02961312491
:do {
    /ip hotspot user add name="02961312491" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02961312491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02961312491";
};

# المستخدم 50: 02269587938
:do {
    /ip hotspot user add name="02269587938" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02269587938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02269587938";
};

# المستخدم 51: 02155925096
:do {
    /ip hotspot user add name="02155925096" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02155925096";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02155925096";
};

# المستخدم 52: 02059587107
:do {
    /ip hotspot user add name="02059587107" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02059587107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02059587107";
};

# المستخدم 53: 02477682868
:do {
    /ip hotspot user add name="02477682868" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02477682868";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02477682868";
};

# المستخدم 54: 02521580671
:do {
    /ip hotspot user add name="02521580671" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02521580671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02521580671";
};

# المستخدم 55: 02276344112
:do {
    /ip hotspot user add name="02276344112" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02276344112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02276344112";
};

# المستخدم 56: 02861353945
:do {
    /ip hotspot user add name="02861353945" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02861353945";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02861353945";
};

# المستخدم 57: 02610754746
:do {
    /ip hotspot user add name="02610754746" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02610754746";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02610754746";
};

# المستخدم 58: 02924878540
:do {
    /ip hotspot user add name="02924878540" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02924878540";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02924878540";
};

# المستخدم 59: 02161745978
:do {
    /ip hotspot user add name="02161745978" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02161745978";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02161745978";
};

# المستخدم 60: 02062569474
:do {
    /ip hotspot user add name="02062569474" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02062569474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02062569474";
};

# المستخدم 61: 02443744137
:do {
    /ip hotspot user add name="02443744137" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02443744137";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02443744137";
};

# المستخدم 62: 02155530012
:do {
    /ip hotspot user add name="02155530012" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02155530012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02155530012";
};

# المستخدم 63: 02742257242
:do {
    /ip hotspot user add name="02742257242" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02742257242";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02742257242";
};

# المستخدم 64: 02472134432
:do {
    /ip hotspot user add name="02472134432" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02472134432";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02472134432";
};

# المستخدم 65: 02696231600
:do {
    /ip hotspot user add name="02696231600" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02696231600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02696231600";
};

# المستخدم 66: 02900132603
:do {
    /ip hotspot user add name="02900132603" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02900132603";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02900132603";
};

# المستخدم 67: 02628061508
:do {
    /ip hotspot user add name="02628061508" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02628061508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02628061508";
};

# المستخدم 68: 02269337305
:do {
    /ip hotspot user add name="02269337305" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02269337305";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02269337305";
};

# المستخدم 69: 02399463216
:do {
    /ip hotspot user add name="02399463216" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02399463216";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02399463216";
};

# المستخدم 70: 02589041417
:do {
    /ip hotspot user add name="02589041417" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02589041417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02589041417";
};

# المستخدم 71: 02621393972
:do {
    /ip hotspot user add name="02621393972" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02621393972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02621393972";
};

# المستخدم 72: 02384390430
:do {
    /ip hotspot user add name="02384390430" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02384390430";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02384390430";
};

# المستخدم 73: 02260812420
:do {
    /ip hotspot user add name="02260812420" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02260812420";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02260812420";
};

# المستخدم 74: 02725780479
:do {
    /ip hotspot user add name="02725780479" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02725780479";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02725780479";
};

# المستخدم 75: 02775514088
:do {
    /ip hotspot user add name="02775514088" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02775514088";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02775514088";
};

# المستخدم 76: 02098468988
:do {
    /ip hotspot user add name="02098468988" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02098468988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02098468988";
};

# المستخدم 77: 02675036682
:do {
    /ip hotspot user add name="02675036682" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02675036682";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02675036682";
};

# المستخدم 78: 02389116197
:do {
    /ip hotspot user add name="02389116197" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02389116197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02389116197";
};

# المستخدم 79: 02502797953
:do {
    /ip hotspot user add name="02502797953" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02502797953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02502797953";
};

# المستخدم 80: 02507275243
:do {
    /ip hotspot user add name="02507275243" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02507275243";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02507275243";
};

# المستخدم 81: 02735985192
:do {
    /ip hotspot user add name="02735985192" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02735985192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02735985192";
};

# المستخدم 82: 02604058186
:do {
    /ip hotspot user add name="02604058186" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02604058186";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02604058186";
};

# المستخدم 83: 02561262773
:do {
    /ip hotspot user add name="02561262773" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02561262773";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02561262773";
};

# المستخدم 84: 02742475532
:do {
    /ip hotspot user add name="02742475532" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02742475532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02742475532";
};

# المستخدم 85: 02211016507
:do {
    /ip hotspot user add name="02211016507" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02211016507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02211016507";
};

# المستخدم 86: 02835041194
:do {
    /ip hotspot user add name="02835041194" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02835041194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02835041194";
};

# المستخدم 87: 02228040831
:do {
    /ip hotspot user add name="02228040831" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02228040831";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02228040831";
};

# المستخدم 88: 02993943576
:do {
    /ip hotspot user add name="02993943576" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02993943576";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02993943576";
};

# المستخدم 89: 02575773970
:do {
    /ip hotspot user add name="02575773970" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02575773970";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02575773970";
};

# المستخدم 90: 02419810271
:do {
    /ip hotspot user add name="02419810271" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02419810271";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02419810271";
};

# المستخدم 91: 02652526973
:do {
    /ip hotspot user add name="02652526973" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02652526973";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02652526973";
};

# المستخدم 92: 02842718111
:do {
    /ip hotspot user add name="02842718111" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02842718111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02842718111";
};

# المستخدم 93: 02922819721
:do {
    /ip hotspot user add name="02922819721" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02922819721";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02922819721";
};

# المستخدم 94: 02113410867
:do {
    /ip hotspot user add name="02113410867" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02113410867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02113410867";
};

# المستخدم 95: 02165187238
:do {
    /ip hotspot user add name="02165187238" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02165187238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02165187238";
};

# المستخدم 96: 02926269497
:do {
    /ip hotspot user add name="02926269497" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02926269497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02926269497";
};

# المستخدم 97: 02972600806
:do {
    /ip hotspot user add name="02972600806" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02972600806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02972600806";
};

# المستخدم 98: 02408398084
:do {
    /ip hotspot user add name="02408398084" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02408398084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02408398084";
};

# المستخدم 99: 02737092986
:do {
    /ip hotspot user add name="02737092986" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02737092986";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02737092986";
};

# المستخدم 100: 02507928440
:do {
    /ip hotspot user add name="02507928440" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02507928440";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02507928440";
};

# المستخدم 101: 02709933691
:do {
    /ip hotspot user add name="02709933691" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02709933691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02709933691";
};

# المستخدم 102: 02220908715
:do {
    /ip hotspot user add name="02220908715" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02220908715";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02220908715";
};

# المستخدم 103: 02211095821
:do {
    /ip hotspot user add name="02211095821" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02211095821";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02211095821";
};

# المستخدم 104: 02142891988
:do {
    /ip hotspot user add name="02142891988" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02142891988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02142891988";
};

# المستخدم 105: 02220947678
:do {
    /ip hotspot user add name="02220947678" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02220947678";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02220947678";
};

# المستخدم 106: 02358392594
:do {
    /ip hotspot user add name="02358392594" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02358392594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02358392594";
};

# المستخدم 107: 02032118371
:do {
    /ip hotspot user add name="02032118371" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02032118371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02032118371";
};

# المستخدم 108: 02668830201
:do {
    /ip hotspot user add name="02668830201" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02668830201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02668830201";
};

# المستخدم 109: 02592820545
:do {
    /ip hotspot user add name="02592820545" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02592820545";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02592820545";
};

# المستخدم 110: 02774964345
:do {
    /ip hotspot user add name="02774964345" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02774964345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02774964345";
};

# المستخدم 111: 02833112470
:do {
    /ip hotspot user add name="02833112470" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02833112470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02833112470";
};

# المستخدم 112: 02418529759
:do {
    /ip hotspot user add name="02418529759" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02418529759";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02418529759";
};

# المستخدم 113: 02477330875
:do {
    /ip hotspot user add name="02477330875" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02477330875";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02477330875";
};

# المستخدم 114: 02628197452
:do {
    /ip hotspot user add name="02628197452" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02628197452";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02628197452";
};

# المستخدم 115: 02731864460
:do {
    /ip hotspot user add name="02731864460" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02731864460";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02731864460";
};

# المستخدم 116: 02619542204
:do {
    /ip hotspot user add name="02619542204" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02619542204";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02619542204";
};

# المستخدم 117: 02189446148
:do {
    /ip hotspot user add name="02189446148" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02189446148";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02189446148";
};

# المستخدم 118: 02714680526
:do {
    /ip hotspot user add name="02714680526" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02714680526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02714680526";
};

# المستخدم 119: 02801531011
:do {
    /ip hotspot user add name="02801531011" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02801531011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02801531011";
};

# المستخدم 120: 02194732683
:do {
    /ip hotspot user add name="02194732683" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02194732683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02194732683";
};

# المستخدم 121: 02211995536
:do {
    /ip hotspot user add name="02211995536" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02211995536";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02211995536";
};

# المستخدم 122: 02562187179
:do {
    /ip hotspot user add name="02562187179" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02562187179";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02562187179";
};

# المستخدم 123: 02953280494
:do {
    /ip hotspot user add name="02953280494" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02953280494";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02953280494";
};

# المستخدم 124: 02230542145
:do {
    /ip hotspot user add name="02230542145" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02230542145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02230542145";
};

# المستخدم 125: 02031452908
:do {
    /ip hotspot user add name="02031452908" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02031452908";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02031452908";
};

# المستخدم 126: 02241081822
:do {
    /ip hotspot user add name="02241081822" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02241081822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02241081822";
};

# المستخدم 127: 02932009954
:do {
    /ip hotspot user add name="02932009954" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02932009954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02932009954";
};

# المستخدم 128: 02261107024
:do {
    /ip hotspot user add name="02261107024" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02261107024";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02261107024";
};

# المستخدم 129: 02459873517
:do {
    /ip hotspot user add name="02459873517" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02459873517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02459873517";
};

# المستخدم 130: 02116405000
:do {
    /ip hotspot user add name="02116405000" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02116405000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02116405000";
};

# المستخدم 131: 02990010819
:do {
    /ip hotspot user add name="02990010819" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02990010819";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02990010819";
};

# المستخدم 132: 02346576075
:do {
    /ip hotspot user add name="02346576075" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02346576075";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02346576075";
};

# المستخدم 133: 02539017120
:do {
    /ip hotspot user add name="02539017120" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02539017120";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02539017120";
};

# المستخدم 134: 02793713645
:do {
    /ip hotspot user add name="02793713645" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02793713645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02793713645";
};

# المستخدم 135: 02666223209
:do {
    /ip hotspot user add name="02666223209" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02666223209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02666223209";
};

# المستخدم 136: 02032951247
:do {
    /ip hotspot user add name="02032951247" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02032951247";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02032951247";
};

# المستخدم 137: 02362839484
:do {
    /ip hotspot user add name="02362839484" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02362839484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02362839484";
};

# المستخدم 138: 02882434793
:do {
    /ip hotspot user add name="02882434793" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02882434793";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02882434793";
};

# المستخدم 139: 02092560021
:do {
    /ip hotspot user add name="02092560021" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02092560021";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02092560021";
};

# المستخدم 140: 02406777405
:do {
    /ip hotspot user add name="02406777405" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02406777405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02406777405";
};

# المستخدم 141: 02423812320
:do {
    /ip hotspot user add name="02423812320" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02423812320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02423812320";
};

# المستخدم 142: 02457018146
:do {
    /ip hotspot user add name="02457018146" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02457018146";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02457018146";
};

# المستخدم 143: 02507922534
:do {
    /ip hotspot user add name="02507922534" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02507922534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02507922534";
};

# المستخدم 144: 02854131675
:do {
    /ip hotspot user add name="02854131675" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02854131675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02854131675";
};

# المستخدم 145: 02359378412
:do {
    /ip hotspot user add name="02359378412" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02359378412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02359378412";
};

# المستخدم 146: 02495840345
:do {
    /ip hotspot user add name="02495840345" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02495840345";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02495840345";
};

# المستخدم 147: 02882653458
:do {
    /ip hotspot user add name="02882653458" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02882653458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02882653458";
};

# المستخدم 148: 02314867588
:do {
    /ip hotspot user add name="02314867588" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02314867588";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02314867588";
};

# المستخدم 149: 02823291865
:do {
    /ip hotspot user add name="02823291865" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02823291865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02823291865";
};

# المستخدم 150: 02467446787
:do {
    /ip hotspot user add name="02467446787" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02467446787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02467446787";
};

# المستخدم 151: 02922920917
:do {
    /ip hotspot user add name="02922920917" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02922920917";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02922920917";
};

# المستخدم 152: 02189321261
:do {
    /ip hotspot user add name="02189321261" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02189321261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02189321261";
};

# المستخدم 153: 02517372140
:do {
    /ip hotspot user add name="02517372140" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02517372140";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02517372140";
};

# المستخدم 154: 02214128694
:do {
    /ip hotspot user add name="02214128694" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02214128694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02214128694";
};

# المستخدم 155: 02957306091
:do {
    /ip hotspot user add name="02957306091" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02957306091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02957306091";
};

# المستخدم 156: 02193964771
:do {
    /ip hotspot user add name="02193964771" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02193964771";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02193964771";
};

# المستخدم 157: 02172054767
:do {
    /ip hotspot user add name="02172054767" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02172054767";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02172054767";
};

# المستخدم 158: 02685516568
:do {
    /ip hotspot user add name="02685516568" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02685516568";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02685516568";
};

# المستخدم 159: 02820983107
:do {
    /ip hotspot user add name="02820983107" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02820983107";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02820983107";
};

# المستخدم 160: 02774586176
:do {
    /ip hotspot user add name="02774586176" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02774586176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02774586176";
};

# المستخدم 161: 02077660534
:do {
    /ip hotspot user add name="02077660534" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02077660534";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02077660534";
};

# المستخدم 162: 02158483795
:do {
    /ip hotspot user add name="02158483795" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02158483795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02158483795";
};

# المستخدم 163: 02643049008
:do {
    /ip hotspot user add name="02643049008" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02643049008";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02643049008";
};

# المستخدم 164: 02779895720
:do {
    /ip hotspot user add name="02779895720" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02779895720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02779895720";
};

# المستخدم 165: 02227302484
:do {
    /ip hotspot user add name="02227302484" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02227302484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02227302484";
};

# المستخدم 166: 02564303833
:do {
    /ip hotspot user add name="02564303833" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02564303833";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02564303833";
};

# المستخدم 167: 02267042818
:do {
    /ip hotspot user add name="02267042818" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02267042818";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02267042818";
};

# المستخدم 168: 02190422936
:do {
    /ip hotspot user add name="02190422936" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02190422936";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02190422936";
};

# المستخدم 169: 02601572942
:do {
    /ip hotspot user add name="02601572942" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02601572942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02601572942";
};

# المستخدم 170: 02260946838
:do {
    /ip hotspot user add name="02260946838" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02260946838";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02260946838";
};

# المستخدم 171: 02175932122
:do {
    /ip hotspot user add name="02175932122" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02175932122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02175932122";
};

# المستخدم 172: 02473003983
:do {
    /ip hotspot user add name="02473003983" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02473003983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02473003983";
};

# المستخدم 173: 02734178612
:do {
    /ip hotspot user add name="02734178612" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02734178612";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02734178612";
};

# المستخدم 174: 02620193218
:do {
    /ip hotspot user add name="02620193218" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02620193218";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02620193218";
};

# المستخدم 175: 02982718000
:do {
    /ip hotspot user add name="02982718000" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02982718000";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02982718000";
};

# المستخدم 176: 02139712049
:do {
    /ip hotspot user add name="02139712049" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02139712049";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02139712049";
};

# المستخدم 177: 02555573950
:do {
    /ip hotspot user add name="02555573950" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02555573950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02555573950";
};

# المستخدم 178: 02471495700
:do {
    /ip hotspot user add name="02471495700" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02471495700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02471495700";
};

# المستخدم 179: 02278699953
:do {
    /ip hotspot user add name="02278699953" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02278699953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02278699953";
};

# المستخدم 180: 02778903215
:do {
    /ip hotspot user add name="02778903215" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02778903215";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02778903215";
};

# المستخدم 181: 02739477687
:do {
    /ip hotspot user add name="02739477687" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02739477687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02739477687";
};

# المستخدم 182: 02875657427
:do {
    /ip hotspot user add name="02875657427" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02875657427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02875657427";
};

# المستخدم 183: 02647636093
:do {
    /ip hotspot user add name="02647636093" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02647636093";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02647636093";
};

# المستخدم 184: 02220862160
:do {
    /ip hotspot user add name="02220862160" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02220862160";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02220862160";
};

# المستخدم 185: 02035326361
:do {
    /ip hotspot user add name="02035326361" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02035326361";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02035326361";
};

# المستخدم 186: 02993807955
:do {
    /ip hotspot user add name="02993807955" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02993807955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02993807955";
};

# المستخدم 187: 02008328097
:do {
    /ip hotspot user add name="02008328097" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02008328097";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02008328097";
};

# المستخدم 188: 02744410926
:do {
    /ip hotspot user add name="02744410926" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02744410926";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02744410926";
};

# المستخدم 189: 02807673168
:do {
    /ip hotspot user add name="02807673168" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02807673168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02807673168";
};

# المستخدم 190: 02459478281
:do {
    /ip hotspot user add name="02459478281" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02459478281";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02459478281";
};

# المستخدم 191: 02373066729
:do {
    /ip hotspot user add name="02373066729" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02373066729";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02373066729";
};

# المستخدم 192: 02266759691
:do {
    /ip hotspot user add name="02266759691" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02266759691";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02266759691";
};

# المستخدم 193: 02836341950
:do {
    /ip hotspot user add name="02836341950" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02836341950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02836341950";
};

# المستخدم 194: 02561491171
:do {
    /ip hotspot user add name="02561491171" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02561491171";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02561491171";
};

# المستخدم 195: 02885467336
:do {
    /ip hotspot user add name="02885467336" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02885467336";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02885467336";
};

# المستخدم 196: 02388247806
:do {
    /ip hotspot user add name="02388247806" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02388247806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02388247806";
};

# المستخدم 197: 02479116457
:do {
    /ip hotspot user add name="02479116457" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02479116457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02479116457";
};

# المستخدم 198: 02392545090
:do {
    /ip hotspot user add name="02392545090" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02392545090";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02392545090";
};

# المستخدم 199: 02724733872
:do {
    /ip hotspot user add name="02724733872" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02724733872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02724733872";
};

# المستخدم 200: 02027958850
:do {
    /ip hotspot user add name="02027958850" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02027958850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02027958850";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
