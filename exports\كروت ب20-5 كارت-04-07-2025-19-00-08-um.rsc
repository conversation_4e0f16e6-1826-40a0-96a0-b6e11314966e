# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 19:00:08
# القالب: 10
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 2005909253
:do {
    /tool user-manager user add customer="admin" username="2005909253" password="08111080" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005909253";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005909253";
};

# المستخدم 2: 2052722725
:do {
    /tool user-manager user add customer="admin" username="2052722725" password="25033686" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052722725";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052722725";
};

# المستخدم 3: 2047886201
:do {
    /tool user-manager user add customer="admin" username="2047886201" password="32459221" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047886201";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047886201";
};

# المستخدم 4: 2081504415
:do {
    /tool user-manager user add customer="admin" username="2081504415" password="97332623" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081504415";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081504415";
};

# المستخدم 5: 2020745879
:do {
    /tool user-manager user add customer="admin" username="2020745879" password="48836619" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020745879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020745879";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
