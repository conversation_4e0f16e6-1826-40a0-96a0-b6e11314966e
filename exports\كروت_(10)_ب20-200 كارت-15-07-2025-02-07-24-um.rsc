# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-15 02:07:24
# القالب: 10
# النظام: user_manager
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 200 مستخدم User Manager...";

# المستخدم 1: 2038671922
:do {
    /tool user-manager user add customer="admin" username="2038671922" password="95892206" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038671922";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038671922";
};

# المستخدم 2: 2031027303
:do {
    /tool user-manager user add customer="admin" username="2031027303" password="93838833" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031027303";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031027303";
};

# المستخدم 3: 2009664136
:do {
    /tool user-manager user add customer="admin" username="2009664136" password="04869759" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009664136";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009664136";
};

# المستخدم 4: 2050194340
:do {
    /tool user-manager user add customer="admin" username="2050194340" password="72769369" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050194340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050194340";
};

# المستخدم 5: 2058212677
:do {
    /tool user-manager user add customer="admin" username="2058212677" password="05601508" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058212677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058212677";
};

# المستخدم 6: 2041453505
:do {
    /tool user-manager user add customer="admin" username="2041453505" password="27335735" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041453505";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041453505";
};

# المستخدم 7: 2051027051
:do {
    /tool user-manager user add customer="admin" username="2051027051" password="89478537" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051027051";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051027051";
};

# المستخدم 8: 2002420195
:do {
    /tool user-manager user add customer="admin" username="2002420195" password="93034743" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002420195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002420195";
};

# المستخدم 9: 2059474259
:do {
    /tool user-manager user add customer="admin" username="2059474259" password="11619608" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059474259";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059474259";
};

# المستخدم 10: 2076635073
:do {
    /tool user-manager user add customer="admin" username="2076635073" password="49017510" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076635073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076635073";
};

# المستخدم 11: 2001386401
:do {
    /tool user-manager user add customer="admin" username="2001386401" password="57836194" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001386401";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001386401";
};

# المستخدم 12: 2078580111
:do {
    /tool user-manager user add customer="admin" username="2078580111" password="08452926" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078580111";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078580111";
};

# المستخدم 13: 2031394794
:do {
    /tool user-manager user add customer="admin" username="2031394794" password="13597936" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031394794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031394794";
};

# المستخدم 14: 2059628285
:do {
    /tool user-manager user add customer="admin" username="2059628285" password="29277468" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059628285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059628285";
};

# المستخدم 15: 2078017153
:do {
    /tool user-manager user add customer="admin" username="2078017153" password="65549339" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078017153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078017153";
};

# المستخدم 16: 2087995938
:do {
    /tool user-manager user add customer="admin" username="2087995938" password="93786341" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087995938";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087995938";
};

# المستخدم 17: 2058764310
:do {
    /tool user-manager user add customer="admin" username="2058764310" password="96068833" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058764310";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058764310";
};

# المستخدم 18: 2031282826
:do {
    /tool user-manager user add customer="admin" username="2031282826" password="68803227" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031282826";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031282826";
};

# المستخدم 19: 2062209441
:do {
    /tool user-manager user add customer="admin" username="2062209441" password="11800372" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2062209441";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2062209441";
};

# المستخدم 20: 2097177032
:do {
    /tool user-manager user add customer="admin" username="2097177032" password="36836625" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097177032";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097177032";
};

# المستخدم 21: 2086535085
:do {
    /tool user-manager user add customer="admin" username="2086535085" password="73031056" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086535085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086535085";
};

# المستخدم 22: 2050977092
:do {
    /tool user-manager user add customer="admin" username="2050977092" password="36123495" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050977092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050977092";
};

# المستخدم 23: 2044702646
:do {
    /tool user-manager user add customer="admin" username="2044702646" password="50457532" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044702646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044702646";
};

# المستخدم 24: 2020257731
:do {
    /tool user-manager user add customer="admin" username="2020257731" password="27527189" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2020257731";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2020257731";
};

# المستخدم 25: 2091378864
:do {
    /tool user-manager user add customer="admin" username="2091378864" password="27330325" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091378864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091378864";
};

# المستخدم 26: 2008785683
:do {
    /tool user-manager user add customer="admin" username="2008785683" password="89979409" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008785683";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008785683";
};

# المستخدم 27: 2035043538
:do {
    /tool user-manager user add customer="admin" username="2035043538" password="96062205" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035043538";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035043538";
};

# المستخدم 28: 2056799559
:do {
    /tool user-manager user add customer="admin" username="2056799559" password="39149512" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056799559";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056799559";
};

# المستخدم 29: 2065748112
:do {
    /tool user-manager user add customer="admin" username="2065748112" password="61548982" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065748112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065748112";
};

# المستخدم 30: 2033040726
:do {
    /tool user-manager user add customer="admin" username="2033040726" password="25713241" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033040726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033040726";
};

# المستخدم 31: 2058113325
:do {
    /tool user-manager user add customer="admin" username="2058113325" password="75052305" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058113325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058113325";
};

# المستخدم 32: 2089779483
:do {
    /tool user-manager user add customer="admin" username="2089779483" password="19805160" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089779483";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089779483";
};

# المستخدم 33: 2032747864
:do {
    /tool user-manager user add customer="admin" username="2032747864" password="74235181" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032747864";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032747864";
};

# المستخدم 34: 2075281123
:do {
    /tool user-manager user add customer="admin" username="2075281123" password="57255622" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075281123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075281123";
};

# المستخدم 35: 2087134589
:do {
    /tool user-manager user add customer="admin" username="2087134589" password="89492923" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087134589";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087134589";
};

# المستخدم 36: 2033784565
:do {
    /tool user-manager user add customer="admin" username="2033784565" password="07781986" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033784565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033784565";
};

# المستخدم 37: 2001934178
:do {
    /tool user-manager user add customer="admin" username="2001934178" password="70118199" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001934178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001934178";
};

# المستخدم 38: 2013587812
:do {
    /tool user-manager user add customer="admin" username="2013587812" password="19826644" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013587812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013587812";
};

# المستخدم 39: 2011900404
:do {
    /tool user-manager user add customer="admin" username="2011900404" password="61683306" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011900404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011900404";
};

# المستخدم 40: 2099425280
:do {
    /tool user-manager user add customer="admin" username="2099425280" password="03580233" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099425280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099425280";
};

# المستخدم 41: 2081185513
:do {
    /tool user-manager user add customer="admin" username="2081185513" password="28555900" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081185513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081185513";
};

# المستخدم 42: 2099107337
:do {
    /tool user-manager user add customer="admin" username="2099107337" password="48750848" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2099107337";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2099107337";
};

# المستخدم 43: 2001863719
:do {
    /tool user-manager user add customer="admin" username="2001863719" password="95919204" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001863719";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001863719";
};

# المستخدم 44: 2055366227
:do {
    /tool user-manager user add customer="admin" username="2055366227" password="22114206" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055366227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055366227";
};

# المستخدم 45: 2090855176
:do {
    /tool user-manager user add customer="admin" username="2090855176" password="57803908" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090855176";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090855176";
};

# المستخدم 46: 2087426762
:do {
    /tool user-manager user add customer="admin" username="2087426762" password="13281180" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087426762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087426762";
};

# المستخدم 47: 2024391599
:do {
    /tool user-manager user add customer="admin" username="2024391599" password="05286561" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024391599";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024391599";
};

# المستخدم 48: 2097171324
:do {
    /tool user-manager user add customer="admin" username="2097171324" password="49364612" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097171324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097171324";
};

# المستخدم 49: 2077577443
:do {
    /tool user-manager user add customer="admin" username="2077577443" password="07802466" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077577443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077577443";
};

# المستخدم 50: 2011618469
:do {
    /tool user-manager user add customer="admin" username="2011618469" password="25741136" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011618469";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011618469";
};

# المستخدم 51: 2022894724
:do {
    /tool user-manager user add customer="admin" username="2022894724" password="81293641" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022894724";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022894724";
};

# المستخدم 52: 2006192754
:do {
    /tool user-manager user add customer="admin" username="2006192754" password="88996453" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006192754";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006192754";
};

# المستخدم 53: 2075766503
:do {
    /tool user-manager user add customer="admin" username="2075766503" password="09373274" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075766503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075766503";
};

# المستخدم 54: 2050693182
:do {
    /tool user-manager user add customer="admin" username="2050693182" password="38360253" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050693182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050693182";
};

# المستخدم 55: 2088110734
:do {
    /tool user-manager user add customer="admin" username="2088110734" password="99430613" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088110734";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088110734";
};

# المستخدم 56: 2069751354
:do {
    /tool user-manager user add customer="admin" username="2069751354" password="78839620" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069751354";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069751354";
};

# المستخدم 57: 2087443012
:do {
    /tool user-manager user add customer="admin" username="2087443012" password="97262664" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087443012";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087443012";
};

# المستخدم 58: 2034720296
:do {
    /tool user-manager user add customer="admin" username="2034720296" password="38702368" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034720296";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034720296";
};

# المستخدم 59: 2036560442
:do {
    /tool user-manager user add customer="admin" username="2036560442" password="29618346" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2036560442";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2036560442";
};

# المستخدم 60: 2087298098
:do {
    /tool user-manager user add customer="admin" username="2087298098" password="37141664" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087298098";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087298098";
};

# المستخدم 61: 2098441686
:do {
    /tool user-manager user add customer="admin" username="2098441686" password="17737995" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098441686";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098441686";
};

# المستخدم 62: 2009895953
:do {
    /tool user-manager user add customer="admin" username="2009895953" password="88299641" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009895953";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009895953";
};

# المستخدم 63: 2040519533
:do {
    /tool user-manager user add customer="admin" username="2040519533" password="01618249" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040519533";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040519533";
};

# المستخدم 64: 2028634193
:do {
    /tool user-manager user add customer="admin" username="2028634193" password="58082618" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028634193";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028634193";
};

# المستخدم 65: 2093159995
:do {
    /tool user-manager user add customer="admin" username="2093159995" password="03764034" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093159995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093159995";
};

# المستخدم 66: 2018932238
:do {
    /tool user-manager user add customer="admin" username="2018932238" password="15364146" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018932238";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018932238";
};

# المستخدم 67: 2007220720
:do {
    /tool user-manager user add customer="admin" username="2007220720" password="28169988" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007220720";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007220720";
};

# المستخدم 68: 2047656108
:do {
    /tool user-manager user add customer="admin" username="2047656108" password="75365160" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047656108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047656108";
};

# المستخدم 69: 2026466318
:do {
    /tool user-manager user add customer="admin" username="2026466318" password="99260297" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026466318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026466318";
};

# المستخدم 70: 2047402189
:do {
    /tool user-manager user add customer="admin" username="2047402189" password="89193252" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2047402189";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2047402189";
};

# المستخدم 71: 2019177092
:do {
    /tool user-manager user add customer="admin" username="2019177092" password="59243344" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019177092";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019177092";
};

# المستخدم 72: 2083261815
:do {
    /tool user-manager user add customer="admin" username="2083261815" password="71910750" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083261815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083261815";
};

# المستخدم 73: 2043215132
:do {
    /tool user-manager user add customer="admin" username="2043215132" password="51556472" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043215132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043215132";
};

# المستخدم 74: 2068581130
:do {
    /tool user-manager user add customer="admin" username="2068581130" password="84517082" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068581130";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068581130";
};

# المستخدم 75: 2032249870
:do {
    /tool user-manager user add customer="admin" username="2032249870" password="17357662" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032249870";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032249870";
};

# المستخدم 76: 2026602180
:do {
    /tool user-manager user add customer="admin" username="2026602180" password="03809425" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026602180";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026602180";
};

# المستخدم 77: 2080457847
:do {
    /tool user-manager user add customer="admin" username="2080457847" password="34779665" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080457847";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080457847";
};

# المستخدم 78: 2014798542
:do {
    /tool user-manager user add customer="admin" username="2014798542" password="03572099" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014798542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014798542";
};

# المستخدم 79: 2079459113
:do {
    /tool user-manager user add customer="admin" username="2079459113" password="77613160" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079459113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079459113";
};

# المستخدم 80: 2024815865
:do {
    /tool user-manager user add customer="admin" username="2024815865" password="92599555" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024815865";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024815865";
};

# المستخدم 81: 2090361967
:do {
    /tool user-manager user add customer="admin" username="2090361967" password="89320637" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090361967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090361967";
};

# المستخدم 82: 2075681252
:do {
    /tool user-manager user add customer="admin" username="2075681252" password="54044805" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075681252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075681252";
};

# المستخدم 83: 2012143448
:do {
    /tool user-manager user add customer="admin" username="2012143448" password="43850220" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012143448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012143448";
};

# المستخدم 84: 2007047464
:do {
    /tool user-manager user add customer="admin" username="2007047464" password="38709995" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007047464";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007047464";
};

# المستخدم 85: 2052738994
:do {
    /tool user-manager user add customer="admin" username="2052738994" password="27240123" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052738994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052738994";
};

# المستخدم 86: 2082125367
:do {
    /tool user-manager user add customer="admin" username="2082125367" password="07501450" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082125367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082125367";
};

# المستخدم 87: 2044786737
:do {
    /tool user-manager user add customer="admin" username="2044786737" password="37544136" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044786737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044786737";
};

# المستخدم 88: 2093728077
:do {
    /tool user-manager user add customer="admin" username="2093728077" password="60388916" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093728077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093728077";
};

# المستخدم 89: 2077025804
:do {
    /tool user-manager user add customer="admin" username="2077025804" password="08362910" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077025804";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077025804";
};

# المستخدم 90: 2061491463
:do {
    /tool user-manager user add customer="admin" username="2061491463" password="02156198" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2061491463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2061491463";
};

# المستخدم 91: 2051460862
:do {
    /tool user-manager user add customer="admin" username="2051460862" password="30897485" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051460862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051460862";
};

# المستخدم 92: 2051144168
:do {
    /tool user-manager user add customer="admin" username="2051144168" password="69914929" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051144168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051144168";
};

# المستخدم 93: 2088811884
:do {
    /tool user-manager user add customer="admin" username="2088811884" password="50488204" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088811884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088811884";
};

# المستخدم 94: 2080241900
:do {
    /tool user-manager user add customer="admin" username="2080241900" password="17221840" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080241900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080241900";
};

# المستخدم 95: 2022635681
:do {
    /tool user-manager user add customer="admin" username="2022635681" password="56797032" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022635681";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022635681";
};

# المستخدم 96: 2086058128
:do {
    /tool user-manager user add customer="admin" username="2086058128" password="64583720" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086058128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086058128";
};

# المستخدم 97: 2019974153
:do {
    /tool user-manager user add customer="admin" username="2019974153" password="09327425" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019974153";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019974153";
};

# المستخدم 98: 2055066636
:do {
    /tool user-manager user add customer="admin" username="2055066636" password="15141085" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2055066636";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2055066636";
};

# المستخدم 99: 2021288048
:do {
    /tool user-manager user add customer="admin" username="2021288048" password="07426610" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021288048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021288048";
};

# المستخدم 100: 2079540073
:do {
    /tool user-manager user add customer="admin" username="2079540073" password="84534103" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079540073";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079540073";
};

# المستخدم 101: 2095802168
:do {
    /tool user-manager user add customer="admin" username="2095802168" password="89257161" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095802168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095802168";
};

# المستخدم 102: 2028153645
:do {
    /tool user-manager user add customer="admin" username="2028153645" password="50413693" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028153645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028153645";
};

# المستخدم 103: 2003161642
:do {
    /tool user-manager user add customer="admin" username="2003161642" password="32370253" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003161642";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003161642";
};

# المستخدم 104: 2057974952
:do {
    /tool user-manager user add customer="admin" username="2057974952" password="30225300" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057974952";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057974952";
};

# المستخدم 105: 2076830290
:do {
    /tool user-manager user add customer="admin" username="2076830290" password="98282739" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076830290";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076830290";
};

# المستخدم 106: 2017800145
:do {
    /tool user-manager user add customer="admin" username="2017800145" password="53520171" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017800145";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017800145";
};

# المستخدم 107: 2093991909
:do {
    /tool user-manager user add customer="admin" username="2093991909" password="49313356" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2093991909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2093991909";
};

# المستخدم 108: 2038981799
:do {
    /tool user-manager user add customer="admin" username="2038981799" password="93345201" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038981799";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038981799";
};

# المستخدم 109: 2021186934
:do {
    /tool user-manager user add customer="admin" username="2021186934" password="60397142" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021186934";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021186934";
};

# المستخدم 110: 2054630168
:do {
    /tool user-manager user add customer="admin" username="2054630168" password="04169576" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054630168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054630168";
};

# المستخدم 111: 2016365907
:do {
    /tool user-manager user add customer="admin" username="2016365907" password="17869494" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016365907";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016365907";
};

# المستخدم 112: 2070050152
:do {
    /tool user-manager user add customer="admin" username="2070050152" password="90771233" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070050152";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070050152";
};

# المستخدم 113: 2053805168
:do {
    /tool user-manager user add customer="admin" username="2053805168" password="42032907" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053805168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053805168";
};

# المستخدم 114: 2053374787
:do {
    /tool user-manager user add customer="admin" username="2053374787" password="53440809" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053374787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053374787";
};

# المستخدم 115: 2006352983
:do {
    /tool user-manager user add customer="admin" username="2006352983" password="38833879" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006352983";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006352983";
};

# المستخدم 116: 2089436060
:do {
    /tool user-manager user add customer="admin" username="2089436060" password="15485892" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089436060";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089436060";
};

# المستخدم 117: 2077530265
:do {
    /tool user-manager user add customer="admin" username="2077530265" password="87927231" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077530265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077530265";
};

# المستخدم 118: 2069990646
:do {
    /tool user-manager user add customer="admin" username="2069990646" password="37547814" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069990646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069990646";
};

# المستخدم 119: 2021945575
:do {
    /tool user-manager user add customer="admin" username="2021945575" password="04226126" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2021945575";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2021945575";
};

# المستخدم 120: 2067593412
:do {
    /tool user-manager user add customer="admin" username="2067593412" password="75900672" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067593412";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067593412";
};

# المستخدم 121: 2048568052
:do {
    /tool user-manager user add customer="admin" username="2048568052" password="18511912" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048568052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048568052";
};

# المستخدم 122: 2044151991
:do {
    /tool user-manager user add customer="admin" username="2044151991" password="77176768" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044151991";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044151991";
};

# المستخدم 123: 2028172797
:do {
    /tool user-manager user add customer="admin" username="2028172797" password="16892868" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028172797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028172797";
};

# المستخدم 124: 2012051023
:do {
    /tool user-manager user add customer="admin" username="2012051023" password="44837276" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012051023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012051023";
};

# المستخدم 125: 2038249631
:do {
    /tool user-manager user add customer="admin" username="2038249631" password="27058355" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038249631";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038249631";
};

# المستخدم 126: 2051443445
:do {
    /tool user-manager user add customer="admin" username="2051443445" password="52559404" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051443445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051443445";
};

# المستخدم 127: 2028507630
:do {
    /tool user-manager user add customer="admin" username="2028507630" password="18868769" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028507630";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028507630";
};

# المستخدم 128: 2086146458
:do {
    /tool user-manager user add customer="admin" username="2086146458" password="85938641" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086146458";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086146458";
};

# المستخدم 129: 2022023879
:do {
    /tool user-manager user add customer="admin" username="2022023879" password="65825384" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2022023879";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2022023879";
};

# المستخدم 130: 2042681901
:do {
    /tool user-manager user add customer="admin" username="2042681901" password="12185458" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042681901";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042681901";
};

# المستخدم 131: 2013094270
:do {
    /tool user-manager user add customer="admin" username="2013094270" password="89812411" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2013094270";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2013094270";
};

# المستخدم 132: 2019926645
:do {
    /tool user-manager user add customer="admin" username="2019926645" password="94911342" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019926645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019926645";
};

# المستخدم 133: 2014541935
:do {
    /tool user-manager user add customer="admin" username="2014541935" password="41307707" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014541935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014541935";
};

# المستخدم 134: 2070941230
:do {
    /tool user-manager user add customer="admin" username="2070941230" password="59924339" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070941230";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070941230";
};

# المستخدم 135: 2023825128
:do {
    /tool user-manager user add customer="admin" username="2023825128" password="97800649" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023825128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023825128";
};

# المستخدم 136: 2014765191
:do {
    /tool user-manager user add customer="admin" username="2014765191" password="94353978" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014765191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014765191";
};

# المستخدم 137: 2072154696
:do {
    /tool user-manager user add customer="admin" username="2072154696" password="78704244" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072154696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072154696";
};

# المستخدم 138: 2077727675
:do {
    /tool user-manager user add customer="admin" username="2077727675" password="54059832" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2077727675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2077727675";
};

# المستخدم 139: 2012470660
:do {
    /tool user-manager user add customer="admin" username="2012470660" password="39737379" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012470660";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012470660";
};

# المستخدم 140: 2052509318
:do {
    /tool user-manager user add customer="admin" username="2052509318" password="90900869" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052509318";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052509318";
};

# المستخدم 141: 2000717891
:do {
    /tool user-manager user add customer="admin" username="2000717891" password="93917083" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000717891";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000717891";
};

# المستخدم 142: 2052232963
:do {
    /tool user-manager user add customer="admin" username="2052232963" password="74560548" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052232963";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052232963";
};

# المستخدم 143: 2051361062
:do {
    /tool user-manager user add customer="admin" username="2051361062" password="19539348" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051361062";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051361062";
};

# المستخدم 144: 2087006543
:do {
    /tool user-manager user add customer="admin" username="2087006543" password="74649346" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087006543";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087006543";
};

# المستخدم 145: 2056555806
:do {
    /tool user-manager user add customer="admin" username="2056555806" password="29818887" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056555806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056555806";
};

# المستخدم 146: 2089909606
:do {
    /tool user-manager user add customer="admin" username="2089909606" password="55965846" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089909606";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089909606";
};

# المستخدم 147: 2038563406
:do {
    /tool user-manager user add customer="admin" username="2038563406" password="90583416" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038563406";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038563406";
};

# المستخدم 148: 2097167042
:do {
    /tool user-manager user add customer="admin" username="2097167042" password="60062993" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2097167042";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2097167042";
};

# المستخدم 149: 2048273324
:do {
    /tool user-manager user add customer="admin" username="2048273324" password="69040163" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2048273324";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2048273324";
};

# المستخدم 150: 2054098207
:do {
    /tool user-manager user add customer="admin" username="2054098207" password="29296338" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054098207";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054098207";
};

# المستخدم 151: 2001601189
:do {
    /tool user-manager user add customer="admin" username="2001601189" password="48049651" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001601189";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001601189";
};

# المستخدم 152: 2053989647
:do {
    /tool user-manager user add customer="admin" username="2053989647" password="19101137" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053989647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053989647";
};

# المستخدم 153: 2081076790
:do {
    /tool user-manager user add customer="admin" username="2081076790" password="23166561" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081076790";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081076790";
};

# المستخدم 154: 2009174929
:do {
    /tool user-manager user add customer="admin" username="2009174929" password="02264256" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009174929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009174929";
};

# المستخدم 155: 2029002629
:do {
    /tool user-manager user add customer="admin" username="2029002629" password="19632108" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029002629";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029002629";
};

# المستخدم 156: 2049058411
:do {
    /tool user-manager user add customer="admin" username="2049058411" password="04493894" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049058411";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049058411";
};

# المستخدم 157: 2027358491
:do {
    /tool user-manager user add customer="admin" username="2027358491" password="78827260" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027358491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027358491";
};

# المستخدم 158: 2081645899
:do {
    /tool user-manager user add customer="admin" username="2081645899" password="83102291" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081645899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081645899";
};

# المستخدم 159: 2034934702
:do {
    /tool user-manager user add customer="admin" username="2034934702" password="95490426" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034934702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034934702";
};

# المستخدم 160: 2037157482
:do {
    /tool user-manager user add customer="admin" username="2037157482" password="61042199" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037157482";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037157482";
};

# المستخدم 161: 2086059011
:do {
    /tool user-manager user add customer="admin" username="2086059011" password="93282898" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086059011";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086059011";
};

# المستخدم 162: 2098336376
:do {
    /tool user-manager user add customer="admin" username="2098336376" password="28108120" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098336376";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098336376";
};

# المستخدم 163: 2002118744
:do {
    /tool user-manager user add customer="admin" username="2002118744" password="76959322" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2002118744";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2002118744";
};

# المستخدم 164: 2096180862
:do {
    /tool user-manager user add customer="admin" username="2096180862" password="17491990" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096180862";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096180862";
};

# المستخدم 165: 2086414718
:do {
    /tool user-manager user add customer="admin" username="2086414718" password="69397604" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086414718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086414718";
};

# المستخدم 166: 2003587646
:do {
    /tool user-manager user add customer="admin" username="2003587646" password="43242958" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003587646";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003587646";
};

# المستخدم 167: 2041144045
:do {
    /tool user-manager user add customer="admin" username="2041144045" password="35924353" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2041144045";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2041144045";
};

# المستخدم 168: 2080129282
:do {
    /tool user-manager user add customer="admin" username="2080129282" password="44813497" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080129282";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080129282";
};

# المستخدم 169: 2044617371
:do {
    /tool user-manager user add customer="admin" username="2044617371" password="20224226" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044617371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044617371";
};

# المستخدم 170: 2076479417
:do {
    /tool user-manager user add customer="admin" username="2076479417" password="45341611" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076479417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076479417";
};

# المستخدم 171: 2080241712
:do {
    /tool user-manager user add customer="admin" username="2080241712" password="53960009" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080241712";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080241712";
};

# المستخدم 172: 2075759964
:do {
    /tool user-manager user add customer="admin" username="2075759964" password="97044048" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075759964";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075759964";
};

# المستخدم 173: 2033711302
:do {
    /tool user-manager user add customer="admin" username="2033711302" password="45418249" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033711302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033711302";
};

# المستخدم 174: 2057545405
:do {
    /tool user-manager user add customer="admin" username="2057545405" password="88558402" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057545405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057545405";
};

# المستخدم 175: 2014407194
:do {
    /tool user-manager user add customer="admin" username="2014407194" password="19576637" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014407194";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014407194";
};

# المستخدم 176: 2023996053
:do {
    /tool user-manager user add customer="admin" username="2023996053" password="26485851" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2023996053";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2023996053";
};

# المستخدم 177: 2081783106
:do {
    /tool user-manager user add customer="admin" username="2081783106" password="59010019" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081783106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081783106";
};

# المستخدم 178: 2075450239
:do {
    /tool user-manager user add customer="admin" username="2075450239" password="27611197" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075450239";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075450239";
};

# المستخدم 179: 2037144850
:do {
    /tool user-manager user add customer="admin" username="2037144850" password="02576834" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037144850";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037144850";
};

# المستخدم 180: 2038525476
:do {
    /tool user-manager user add customer="admin" username="2038525476" password="20454520" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038525476";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038525476";
};

# المستخدم 181: 2040336703
:do {
    /tool user-manager user add customer="admin" username="2040336703" password="47124870" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2040336703";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2040336703";
};

# المستخدم 182: 2080724508
:do {
    /tool user-manager user add customer="admin" username="2080724508" password="40290117" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080724508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080724508";
};

# المستخدم 183: 2028283191
:do {
    /tool user-manager user add customer="admin" username="2028283191" password="32717049" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028283191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028283191";
};

# المستخدم 184: 2080718738
:do {
    /tool user-manager user add customer="admin" username="2080718738" password="88298924" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080718738";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080718738";
};

# المستخدم 185: 2089618123
:do {
    /tool user-manager user add customer="admin" username="2089618123" password="30525558" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089618123";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089618123";
};

# المستخدم 186: 2011685294
:do {
    /tool user-manager user add customer="admin" username="2011685294" password="56156472" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2011685294";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2011685294";
};

# المستخدم 187: 2058677367
:do {
    /tool user-manager user add customer="admin" username="2058677367" password="43034473" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058677367";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058677367";
};

# المستخدم 188: 2042784786
:do {
    /tool user-manager user add customer="admin" username="2042784786" password="23000346" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042784786";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042784786";
};

# المستخدم 189: 2086586562
:do {
    /tool user-manager user add customer="admin" username="2086586562" password="27989963" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086586562";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086586562";
};

# المستخدم 190: 2051832939
:do {
    /tool user-manager user add customer="admin" username="2051832939" password="48749048" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051832939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051832939";
};

# المستخدم 191: 2014893099
:do {
    /tool user-manager user add customer="admin" username="2014893099" password="90918993" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2014893099";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2014893099";
};

# المستخدم 192: 2029454993
:do {
    /tool user-manager user add customer="admin" username="2029454993" password="71470185" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029454993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029454993";
};

# المستخدم 193: 2069308510
:do {
    /tool user-manager user add customer="admin" username="2069308510" password="74446035" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069308510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069308510";
};

# المستخدم 194: 2059049395
:do {
    /tool user-manager user add customer="admin" username="2059049395" password="09118058" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059049395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059049395";
};

# المستخدم 195: 2001231057
:do {
    /tool user-manager user add customer="admin" username="2001231057" password="98420891" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001231057";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001231057";
};

# المستخدم 196: 2017243387
:do {
    /tool user-manager user add customer="admin" username="2017243387" password="93931989" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017243387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017243387";
};

# المستخدم 197: 2091843077
:do {
    /tool user-manager user add customer="admin" username="2091843077" password="75058896" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091843077";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091843077";
};

# المستخدم 198: 2069796182
:do {
    /tool user-manager user add customer="admin" username="2069796182" password="03610166" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069796182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069796182";
};

# المستخدم 199: 2000983106
:do {
    /tool user-manager user add customer="admin" username="2000983106" password="69755205" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000983106";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000983106";
};

# المستخدم 200: 2059311955
:do {
    /tool user-manager user add customer="admin" username="2059311955" password="53911863" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059311955";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059311955";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
