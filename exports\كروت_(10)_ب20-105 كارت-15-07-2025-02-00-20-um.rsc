# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-15 02:00:20
# القالب: 10
# النظام: user_manager
# عدد الكروت: 105
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 105";

:local success 0;
:local errors 0;
:local total 105;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 105 مستخدم User Manager...";

# المستخدم 1: 2049354010
:do {
    /tool user-manager user add customer="admin" username="2049354010" password="43020455" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049354010";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049354010";
};

# المستخدم 2: 2026518665
:do {
    /tool user-manager user add customer="admin" username="2026518665" password="93218002" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2026518665";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2026518665";
};

# المستخدم 3: 2025470730
:do {
    /tool user-manager user add customer="admin" username="2025470730" password="31670379" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2025470730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2025470730";
};

# المستخدم 4: 2070487607
:do {
    /tool user-manager user add customer="admin" username="2070487607" password="46950672" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070487607";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070487607";
};

# المستخدم 5: 2060786699
:do {
    /tool user-manager user add customer="admin" username="2060786699" password="09704796" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060786699";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060786699";
};

# المستخدم 6: 2068795131
:do {
    /tool user-manager user add customer="admin" username="2068795131" password="35044385" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068795131";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068795131";
};

# المستخدم 7: 2008617365
:do {
    /tool user-manager user add customer="admin" username="2008617365" password="97214106" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008617365";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008617365";
};

# المستخدم 8: 2058773737
:do {
    /tool user-manager user add customer="admin" username="2058773737" password="95949455" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058773737";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058773737";
};

# المستخدم 9: 2015119652
:do {
    /tool user-manager user add customer="admin" username="2015119652" password="16268166" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2015119652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2015119652";
};

# المستخدم 10: 2052641214
:do {
    /tool user-manager user add customer="admin" username="2052641214" password="19382320" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052641214";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052641214";
};

# المستخدم 11: 2063302708
:do {
    /tool user-manager user add customer="admin" username="2063302708" password="21312003" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063302708";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063302708";
};

# المستخدم 12: 2064283084
:do {
    /tool user-manager user add customer="admin" username="2064283084" password="16297751" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064283084";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064283084";
};

# المستخدم 13: 2076024655
:do {
    /tool user-manager user add customer="admin" username="2076024655" password="73811726" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2076024655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2076024655";
};

# المستخدم 14: 2042519094
:do {
    /tool user-manager user add customer="admin" username="2042519094" password="34857279" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042519094";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042519094";
};

# المستخدم 15: 2054367594
:do {
    /tool user-manager user add customer="admin" username="2054367594" password="99571805" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054367594";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054367594";
};

# المستخدم 16: 2079524749
:do {
    /tool user-manager user add customer="admin" username="2079524749" password="84521258" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2079524749";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2079524749";
};

# المستخدم 17: 2057078173
:do {
    /tool user-manager user add customer="admin" username="2057078173" password="76182836" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057078173";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057078173";
};

# المستخدم 18: 2095814382
:do {
    /tool user-manager user add customer="admin" username="2095814382" password="57847039" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2095814382";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2095814382";
};

# المستخدم 19: 2043301445
:do {
    /tool user-manager user add customer="admin" username="2043301445" password="36962816" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043301445";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043301445";
};

# المستخدم 20: 2003899066
:do {
    /tool user-manager user add customer="admin" username="2003899066" password="41438106" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2003899066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2003899066";
};

# المستخدم 21: 2042095340
:do {
    /tool user-manager user add customer="admin" username="2042095340" password="93189973" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042095340";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042095340";
};

# المستخدم 22: 2075658346
:do {
    /tool user-manager user add customer="admin" username="2075658346" password="04685718" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075658346";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075658346";
};

# المستخدم 23: 2096863097
:do {
    /tool user-manager user add customer="admin" username="2096863097" password="07221915" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096863097";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096863097";
};

# المستخدم 24: 2085926948
:do {
    /tool user-manager user add customer="admin" username="2085926948" password="64845994" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085926948";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085926948";
};

# المستخدم 25: 2019915408
:do {
    /tool user-manager user add customer="admin" username="2019915408" password="16825255" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019915408";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019915408";
};

# المستخدم 26: 2064248454
:do {
    /tool user-manager user add customer="admin" username="2064248454" password="96085412" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064248454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064248454";
};

# المستخدم 27: 2090126471
:do {
    /tool user-manager user add customer="admin" username="2090126471" password="21630652" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090126471";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090126471";
};

# المستخدم 28: 2065234147
:do {
    /tool user-manager user add customer="admin" username="2065234147" password="44393102" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2065234147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2065234147";
};

# المستخدم 29: 2051158762
:do {
    /tool user-manager user add customer="admin" username="2051158762" password="95015528" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2051158762";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2051158762";
};

# المستخدم 30: 2049712448
:do {
    /tool user-manager user add customer="admin" username="2049712448" password="73131187" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049712448";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049712448";
};

# المستخدم 31: 2039256697
:do {
    /tool user-manager user add customer="admin" username="2039256697" password="24513499" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039256697";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039256697";
};

# المستخدم 32: 2073607617
:do {
    /tool user-manager user add customer="admin" username="2073607617" password="92749211" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073607617";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073607617";
};

# المستخدم 33: 2063816803
:do {
    /tool user-manager user add customer="admin" username="2063816803" password="89603988" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063816803";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063816803";
};

# المستخدم 34: 2081471867
:do {
    /tool user-manager user add customer="admin" username="2081471867" password="32381493" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2081471867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2081471867";
};

# المستخدم 35: 2078070491
:do {
    /tool user-manager user add customer="admin" username="2078070491" password="41482851" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2078070491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2078070491";
};

# المستخدم 36: 2016810808
:do {
    /tool user-manager user add customer="admin" username="2016810808" password="74829585" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016810808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016810808";
};

# المستخدم 37: 2080131552
:do {
    /tool user-manager user add customer="admin" username="2080131552" password="52985192" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080131552";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080131552";
};

# المستخدم 38: 2053628526
:do {
    /tool user-manager user add customer="admin" username="2053628526" password="59239109" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2053628526";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2053628526";
};

# المستخدم 39: 2057953902
:do {
    /tool user-manager user add customer="admin" username="2057953902" password="14312547" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057953902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057953902";
};

# المستخدم 40: 2073390842
:do {
    /tool user-manager user add customer="admin" username="2073390842" password="33624784" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2073390842";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2073390842";
};

# المستخدم 41: 2063885331
:do {
    /tool user-manager user add customer="admin" username="2063885331" password="05566564" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2063885331";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2063885331";
};

# المستخدم 42: 2060376447
:do {
    /tool user-manager user add customer="admin" username="2060376447" password="63712169" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2060376447";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2060376447";
};

# المستخدم 43: 2001160465
:do {
    /tool user-manager user add customer="admin" username="2001160465" password="23176895" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001160465";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001160465";
};

# المستخدم 44: 2038225677
:do {
    /tool user-manager user add customer="admin" username="2038225677" password="99429608" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2038225677";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2038225677";
};

# المستخدم 45: 2085240120
:do {
    /tool user-manager user add customer="admin" username="2085240120" password="09046073" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2085240120";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2085240120";
};

# المستخدم 46: 2075316701
:do {
    /tool user-manager user add customer="admin" username="2075316701" password="13474202" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075316701";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075316701";
};

# المستخدم 47: 2086971996
:do {
    /tool user-manager user add customer="admin" username="2086971996" password="87089323" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086971996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086971996";
};

# المستخدم 48: 2042876117
:do {
    /tool user-manager user add customer="admin" username="2042876117" password="24502348" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2042876117";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2042876117";
};

# المستخدم 49: 2070181822
:do {
    /tool user-manager user add customer="admin" username="2070181822" password="69053270" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2070181822";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2070181822";
};

# المستخدم 50: 2032023752
:do {
    /tool user-manager user add customer="admin" username="2032023752" password="98722675" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032023752";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032023752";
};

# المستخدم 51: 2044587132
:do {
    /tool user-manager user add customer="admin" username="2044587132" password="73534580" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2044587132";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2044587132";
};

# المستخدم 52: 2054948307
:do {
    /tool user-manager user add customer="admin" username="2054948307" password="07009474" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054948307";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054948307";
};

# المستخدم 53: 2039652628
:do {
    /tool user-manager user add customer="admin" username="2039652628" password="25451699" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039652628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039652628";
};

# المستخدم 54: 2084658124
:do {
    /tool user-manager user add customer="admin" username="2084658124" password="48893045" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084658124";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084658124";
};

# المستخدم 55: 2091000794
:do {
    /tool user-manager user add customer="admin" username="2091000794" password="62966247" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091000794";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091000794";
};

# المستخدم 56: 2094326081
:do {
    /tool user-manager user add customer="admin" username="2094326081" password="14185196" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2094326081";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2094326081";
};

# المستخدم 57: 2049520509
:do {
    /tool user-manager user add customer="admin" username="2049520509" password="68250879" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049520509";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049520509";
};

# المستخدم 58: 2080722079
:do {
    /tool user-manager user add customer="admin" username="2080722079" password="05851853" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2080722079";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2080722079";
};

# المستخدم 59: 2074268707
:do {
    /tool user-manager user add customer="admin" username="2074268707" password="98959291" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2074268707";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2074268707";
};

# المستخدم 60: 2067778061
:do {
    /tool user-manager user add customer="admin" username="2067778061" password="59675019" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2067778061";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2067778061";
};

# المستخدم 61: 2000932108
:do {
    /tool user-manager user add customer="admin" username="2000932108" password="09570567" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000932108";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000932108";
};

# المستخدم 62: 2027160405
:do {
    /tool user-manager user add customer="admin" username="2027160405" password="62407333" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2027160405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2027160405";
};

# المستخدم 63: 2096331185
:do {
    /tool user-manager user add customer="admin" username="2096331185" password="43114284" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2096331185";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2096331185";
};

# المستخدم 64: 2018870674
:do {
    /tool user-manager user add customer="admin" username="2018870674" password="45659592" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018870674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018870674";
};

# المستخدم 65: 2064855643
:do {
    /tool user-manager user add customer="admin" username="2064855643" password="01712144" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2064855643";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2064855643";
};

# المستخدم 66: 2033885305
:do {
    /tool user-manager user add customer="admin" username="2033885305" password="48146860" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033885305";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033885305";
};

# المستخدم 67: 2090068475
:do {
    /tool user-manager user add customer="admin" username="2090068475" password="35111943" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090068475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090068475";
};

# المستخدم 68: 2054075651
:do {
    /tool user-manager user add customer="admin" username="2054075651" password="46312818" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054075651";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054075651";
};

# المستخدم 69: 2000344067
:do {
    /tool user-manager user add customer="admin" username="2000344067" password="38418774" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000344067";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000344067";
};

# المستخدم 70: 2087947029
:do {
    /tool user-manager user add customer="admin" username="2087947029" password="62838233" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087947029";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087947029";
};

# المستخدم 71: 2037674867
:do {
    /tool user-manager user add customer="admin" username="2037674867" password="73431990" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037674867";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037674867";
};

# المستخدم 72: 2046710954
:do {
    /tool user-manager user add customer="admin" username="2046710954" password="66230777" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046710954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046710954";
};

# المستخدم 73: 2046113913
:do {
    /tool user-manager user add customer="admin" username="2046113913" password="99414475" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046113913";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046113913";
};

# المستخدم 74: 2088305158
:do {
    /tool user-manager user add customer="admin" username="2088305158" password="71190744" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2088305158";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2088305158";
};

# المستخدم 75: 2057088547
:do {
    /tool user-manager user add customer="admin" username="2057088547" password="74794952" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2057088547";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2057088547";
};

# المستخدم 76: 2012004202
:do {
    /tool user-manager user add customer="admin" username="2012004202" password="60558535" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012004202";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012004202";
};

# المستخدم 77: 2035015835
:do {
    /tool user-manager user add customer="admin" username="2035015835" password="85275203" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035015835";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035015835";
};

# المستخدم 78: 2031675178
:do {
    /tool user-manager user add customer="admin" username="2031675178" password="79538877" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031675178";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031675178";
};

# المستخدم 79: 2012018897
:do {
    /tool user-manager user add customer="admin" username="2012018897" password="15784908" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012018897";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012018897";
};

# المستخدم 80: 2007412694
:do {
    /tool user-manager user add customer="admin" username="2007412694" password="37487124" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2007412694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2007412694";
};

# المستخدم 81: 2019519605
:do {
    /tool user-manager user add customer="admin" username="2019519605" password="72676420" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2019519605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2019519605";
};

# المستخدم 82: 2066560535
:do {
    /tool user-manager user add customer="admin" username="2066560535" password="88230996" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2066560535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2066560535";
};

# المستخدم 83: 2091956497
:do {
    /tool user-manager user add customer="admin" username="2091956497" password="27772607" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2091956497";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2091956497";
};

# المستخدم 84: 2001415394
:do {
    /tool user-manager user add customer="admin" username="2001415394" password="47616879" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001415394";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001415394";
};

# المستخدم 85: 2033671200
:do {
    /tool user-manager user add customer="admin" username="2033671200" password="07155476" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2033671200";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2033671200";
};

# المستخدم 86: 2049735175
:do {
    /tool user-manager user add customer="admin" username="2049735175" password="83387445" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2049735175";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2049735175";
};

# المستخدم 87: 2084596622
:do {
    /tool user-manager user add customer="admin" username="2084596622" password="64899871" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2084596622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2084596622";
};

# المستخدم 88: 2035472398
:do {
    /tool user-manager user add customer="admin" username="2035472398" password="35882071" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035472398";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035472398";
};

# المستخدم 89: 2000068321
:do {
    /tool user-manager user add customer="admin" username="2000068321" password="88158801" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000068321";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000068321";
};

# المستخدم 90: 2056758935
:do {
    /tool user-manager user add customer="admin" username="2056758935" password="28780556" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2056758935";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2056758935";
};

# المستخدم 91: 2034579950
:do {
    /tool user-manager user add customer="admin" username="2034579950" password="35764295" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2034579950";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2034579950";
};

# المستخدم 92: 2054964059
:do {
    /tool user-manager user add customer="admin" username="2054964059" password="91977951" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2054964059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2054964059";
};

# المستخدم 93: 2029160459
:do {
    /tool user-manager user add customer="admin" username="2029160459" password="95253379" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029160459";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029160459";
};

# المستخدم 94: 2016016065
:do {
    /tool user-manager user add customer="admin" username="2016016065" password="02183553" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2016016065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2016016065";
};

# المستخدم 95: 2072775798
:do {
    /tool user-manager user add customer="admin" username="2072775798" password="82981891" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2072775798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2072775798";
};

# المستخدم 96: 2039206256
:do {
    /tool user-manager user add customer="admin" username="2039206256" password="25180019" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2039206256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2039206256";
};

# المستخدم 97: 2083214285
:do {
    /tool user-manager user add customer="admin" username="2083214285" password="94987929" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2083214285";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2083214285";
};

# المستخدم 98: 2018705993
:do {
    /tool user-manager user add customer="admin" username="2018705993" password="15763033" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2018705993";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2018705993";
};

# المستخدم 99: 2000341544
:do {
    /tool user-manager user add customer="admin" username="2000341544" password="80765778" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000341544";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000341544";
};

# المستخدم 100: 2035397972
:do {
    /tool user-manager user add customer="admin" username="2035397972" password="03909825" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2035397972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2035397972";
};

# المستخدم 101: 2012971873
:do {
    /tool user-manager user add customer="admin" username="2012971873" password="04684905" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2012971873";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2012971873";
};

# المستخدم 102: 2017970443
:do {
    /tool user-manager user add customer="admin" username="2017970443" password="72737126" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2017970443";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2017970443";
};

# المستخدم 103: 2092831655
:do {
    /tool user-manager user add customer="admin" username="2092831655" password="85549959" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2092831655";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2092831655";
};

# المستخدم 104: 2024187191
:do {
    /tool user-manager user add customer="admin" username="2024187191" password="48911595" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024187191";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024187191";
};

# المستخدم 105: 2052110675
:do {
    /tool user-manager user add customer="admin" username="2052110675" password="26050314" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2052110675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2052110675";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
