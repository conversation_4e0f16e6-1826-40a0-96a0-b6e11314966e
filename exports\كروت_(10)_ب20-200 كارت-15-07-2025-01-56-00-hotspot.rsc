# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-15 01:56:00
# القالب: 10
# النظام: hotspot
# عدد الكروت: 200
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 200";

:local success 0;
:local errors 0;
:local total 200;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 200 مستخدم Hotspot...";

# المستخدم 1: 02472068756
:do {
    /ip hotspot user add name="02472068756" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02472068756";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02472068756";
};

# المستخدم 2: 02039859454
:do {
    /ip hotspot user add name="02039859454" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02039859454";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02039859454";
};

# المستخدم 3: 02607214366
:do {
    /ip hotspot user add name="02607214366" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02607214366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02607214366";
};

# المستخدم 4: 02212865694
:do {
    /ip hotspot user add name="02212865694" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02212865694";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02212865694";
};

# المستخدم 5: 02200856461
:do {
    /ip hotspot user add name="02200856461" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02200856461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02200856461";
};

# المستخدم 6: 02976343122
:do {
    /ip hotspot user add name="02976343122" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02976343122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02976343122";
};

# المستخدم 7: 02638911855
:do {
    /ip hotspot user add name="02638911855" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02638911855";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02638911855";
};

# المستخدم 8: 02241684767
:do {
    /ip hotspot user add name="02241684767" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02241684767";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02241684767";
};

# المستخدم 9: 02775701954
:do {
    /ip hotspot user add name="02775701954" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02775701954";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02775701954";
};

# المستخدم 10: 02485492385
:do {
    /ip hotspot user add name="02485492385" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02485492385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02485492385";
};

# المستخدم 11: 02018446508
:do {
    /ip hotspot user add name="02018446508" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02018446508";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02018446508";
};

# المستخدم 12: 02410634118
:do {
    /ip hotspot user add name="02410634118" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02410634118";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02410634118";
};

# المستخدم 13: 02574548748
:do {
    /ip hotspot user add name="02574548748" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02574548748";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02574548748";
};

# المستخدم 14: 02423172730
:do {
    /ip hotspot user add name="02423172730" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02423172730";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02423172730";
};

# المستخدم 15: 02861384696
:do {
    /ip hotspot user add name="02861384696" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02861384696";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02861384696";
};

# المستخدم 16: 02812504556
:do {
    /ip hotspot user add name="02812504556" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02812504556";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02812504556";
};

# المستخدم 17: 02772513284
:do {
    /ip hotspot user add name="02772513284" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02772513284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02772513284";
};

# المستخدم 18: 02039298168
:do {
    /ip hotspot user add name="02039298168" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02039298168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02039298168";
};

# المستخدم 19: 02450369128
:do {
    /ip hotspot user add name="02450369128" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02450369128";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02450369128";
};

# المستخدم 20: 02472713046
:do {
    /ip hotspot user add name="02472713046" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02472713046";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02472713046";
};

# المستخدم 21: 02135132366
:do {
    /ip hotspot user add name="02135132366" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02135132366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02135132366";
};

# المستخدم 22: 02322460395
:do {
    /ip hotspot user add name="02322460395" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02322460395";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02322460395";
};

# المستخدم 23: 02741349637
:do {
    /ip hotspot user add name="02741349637" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02741349637";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02741349637";
};

# المستخدم 24: 02265474671
:do {
    /ip hotspot user add name="02265474671" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02265474671";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02265474671";
};

# المستخدم 25: 02619931675
:do {
    /ip hotspot user add name="02619931675" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02619931675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02619931675";
};

# المستخدم 26: 02663059059
:do {
    /ip hotspot user add name="02663059059" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02663059059";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02663059059";
};

# المستخدم 27: 02449793676
:do {
    /ip hotspot user add name="02449793676" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02449793676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02449793676";
};

# المستخدم 28: 02724119424
:do {
    /ip hotspot user add name="02724119424" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02724119424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02724119424";
};

# المستخدم 29: 02136398023
:do {
    /ip hotspot user add name="02136398023" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02136398023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02136398023";
};

# المستخدم 30: 02202450764
:do {
    /ip hotspot user add name="02202450764" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02202450764";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02202450764";
};

# المستخدم 31: 02480503718
:do {
    /ip hotspot user add name="02480503718" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02480503718";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02480503718";
};

# المستخدم 32: 02861423787
:do {
    /ip hotspot user add name="02861423787" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02861423787";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02861423787";
};

# المستخدم 33: 02651858726
:do {
    /ip hotspot user add name="02651858726" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02651858726";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02651858726";
};

# المستخدم 34: 02579048168
:do {
    /ip hotspot user add name="02579048168" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02579048168";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02579048168";
};

# المستخدم 35: 02892630156
:do {
    /ip hotspot user add name="02892630156" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02892630156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02892630156";
};

# المستخدم 36: 02761500622
:do {
    /ip hotspot user add name="02761500622" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02761500622";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02761500622";
};

# المستخدم 37: 02170085582
:do {
    /ip hotspot user add name="02170085582" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02170085582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02170085582";
};

# المستخدم 38: 02054632066
:do {
    /ip hotspot user add name="02054632066" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02054632066";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02054632066";
};

# المستخدم 39: 02760135602
:do {
    /ip hotspot user add name="02760135602" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02760135602";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02760135602";
};

# المستخدم 40: 02264093995
:do {
    /ip hotspot user add name="02264093995" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02264093995";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02264093995";
};

# المستخدم 41: 02576831674
:do {
    /ip hotspot user add name="02576831674" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02576831674";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02576831674";
};

# المستخدم 42: 02741739438
:do {
    /ip hotspot user add name="02741739438" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02741739438";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02741739438";
};

# المستخدم 43: 02422718287
:do {
    /ip hotspot user add name="02422718287" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02422718287";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02422718287";
};

# المستخدم 44: 02601107371
:do {
    /ip hotspot user add name="02601107371" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02601107371";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02601107371";
};

# المستخدم 45: 02629868611
:do {
    /ip hotspot user add name="02629868611" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02629868611";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02629868611";
};

# المستخدم 46: 02730102113
:do {
    /ip hotspot user add name="02730102113" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02730102113";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02730102113";
};

# المستخدم 47: 02613803480
:do {
    /ip hotspot user add name="02613803480" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02613803480";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02613803480";
};

# المستخدم 48: 02767153156
:do {
    /ip hotspot user add name="02767153156" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02767153156";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02767153156";
};

# المستخدم 49: 02151278600
:do {
    /ip hotspot user add name="02151278600" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02151278600";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02151278600";
};

# المستخدم 50: 02156705450
:do {
    /ip hotspot user add name="02156705450" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02156705450";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02156705450";
};

# المستخدم 51: 02191905121
:do {
    /ip hotspot user add name="02191905121" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02191905121";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02191905121";
};

# المستخدم 52: 02844766798
:do {
    /ip hotspot user add name="02844766798" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02844766798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02844766798";
};

# المستخدم 53: 02474143387
:do {
    /ip hotspot user add name="02474143387" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02474143387";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02474143387";
};

# المستخدم 54: 02245524187
:do {
    /ip hotspot user add name="02245524187" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02245524187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02245524187";
};

# المستخدم 55: 02603229572
:do {
    /ip hotspot user add name="02603229572" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02603229572";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02603229572";
};

# المستخدم 56: 02345886050
:do {
    /ip hotspot user add name="02345886050" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02345886050";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02345886050";
};

# المستخدم 57: 02200690085
:do {
    /ip hotspot user add name="02200690085" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02200690085";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02200690085";
};

# المستخدم 58: 02959982969
:do {
    /ip hotspot user add name="02959982969" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02959982969";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02959982969";
};

# المستخدم 59: 02543351474
:do {
    /ip hotspot user add name="02543351474" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02543351474";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02543351474";
};

# المستخدم 60: 02836482961
:do {
    /ip hotspot user add name="02836482961" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02836482961";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02836482961";
};

# المستخدم 61: 02982000076
:do {
    /ip hotspot user add name="02982000076" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02982000076";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02982000076";
};

# المستخدم 62: 02017685299
:do {
    /ip hotspot user add name="02017685299" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02017685299";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02017685299";
};

# المستخدم 63: 02928902039
:do {
    /ip hotspot user add name="02928902039" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02928902039";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02928902039";
};

# المستخدم 64: 02962553824
:do {
    /ip hotspot user add name="02962553824" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02962553824";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02962553824";
};

# المستخدم 65: 02359990052
:do {
    /ip hotspot user add name="02359990052" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02359990052";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02359990052";
};

# المستخدم 66: 02120265900
:do {
    /ip hotspot user add name="02120265900" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02120265900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02120265900";
};

# المستخدم 67: 02735141317
:do {
    /ip hotspot user add name="02735141317" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02735141317";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02735141317";
};

# المستخدم 68: 02949800444
:do {
    /ip hotspot user add name="02949800444" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02949800444";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02949800444";
};

# المستخدم 69: 02673785195
:do {
    /ip hotspot user add name="02673785195" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02673785195";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02673785195";
};

# المستخدم 70: 02633651404
:do {
    /ip hotspot user add name="02633651404" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02633651404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02633651404";
};

# المستخدم 71: 02935107333
:do {
    /ip hotspot user add name="02935107333" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02935107333";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02935107333";
};

# المستخدم 72: 02679300836
:do {
    /ip hotspot user add name="02679300836" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02679300836";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02679300836";
};

# المستخدم 73: 02774719463
:do {
    /ip hotspot user add name="02774719463" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02774719463";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02774719463";
};

# المستخدم 74: 02958720433
:do {
    /ip hotspot user add name="02958720433" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02958720433";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02958720433";
};

# المستخدم 75: 02961927613
:do {
    /ip hotspot user add name="02961927613" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02961927613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02961927613";
};

# المستخدم 76: 02148749929
:do {
    /ip hotspot user add name="02148749929" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02148749929";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02148749929";
};

# المستخدم 77: 02692276535
:do {
    /ip hotspot user add name="02692276535" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02692276535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02692276535";
};

# المستخدم 78: 02722644928
:do {
    /ip hotspot user add name="02722644928" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02722644928";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02722644928";
};

# المستخدم 79: 02077061962
:do {
    /ip hotspot user add name="02077061962" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02077061962";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02077061962";
};

# المستخدم 80: 02371496899
:do {
    /ip hotspot user add name="02371496899" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02371496899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02371496899";
};

# المستخدم 81: 02643574656
:do {
    /ip hotspot user add name="02643574656" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02643574656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02643574656";
};

# المستخدم 82: 02916306112
:do {
    /ip hotspot user add name="02916306112" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02916306112";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02916306112";
};

# المستخدم 83: 02927904301
:do {
    /ip hotspot user add name="02927904301" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02927904301";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02927904301";
};

# المستخدم 84: 02858853261
:do {
    /ip hotspot user add name="02858853261" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02858853261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02858853261";
};

# المستخدم 85: 02571184475
:do {
    /ip hotspot user add name="02571184475" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02571184475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02571184475";
};

# المستخدم 86: 02829833981
:do {
    /ip hotspot user add name="02829833981" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02829833981";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02829833981";
};

# المستخدم 87: 02502858535
:do {
    /ip hotspot user add name="02502858535" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02502858535";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02502858535";
};

# المستخدم 88: 02783355472
:do {
    /ip hotspot user add name="02783355472" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02783355472";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02783355472";
};

# المستخدم 89: 02157380565
:do {
    /ip hotspot user add name="02157380565" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02157380565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02157380565";
};

# المستخدم 90: 02218350939
:do {
    /ip hotspot user add name="02218350939" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02218350939";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02218350939";
};

# المستخدم 91: 02340353245
:do {
    /ip hotspot user add name="02340353245" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02340353245";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02340353245";
};

# المستخدم 92: 02871960291
:do {
    /ip hotspot user add name="02871960291" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02871960291";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02871960291";
};

# المستخدم 93: 02177497141
:do {
    /ip hotspot user add name="02177497141" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02177497141";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02177497141";
};

# المستخدم 94: 02160354325
:do {
    /ip hotspot user add name="02160354325" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02160354325";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02160354325";
};

# المستخدم 95: 02982889532
:do {
    /ip hotspot user add name="02982889532" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02982889532";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02982889532";
};

# المستخدم 96: 02281560874
:do {
    /ip hotspot user add name="02281560874" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02281560874";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02281560874";
};

# المستخدم 97: 02431828808
:do {
    /ip hotspot user add name="02431828808" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02431828808";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02431828808";
};

# المستخدم 98: 02668343391
:do {
    /ip hotspot user add name="02668343391" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02668343391";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02668343391";
};

# المستخدم 99: 02038004860
:do {
    /ip hotspot user add name="02038004860" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02038004860";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02038004860";
};

# المستخدم 100: 02534670227
:do {
    /ip hotspot user add name="02534670227" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02534670227";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02534670227";
};

# المستخدم 101: 02410731377
:do {
    /ip hotspot user add name="02410731377" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02410731377";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02410731377";
};

# المستخدم 102: 02856501510
:do {
    /ip hotspot user add name="02856501510" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02856501510";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02856501510";
};

# المستخدم 103: 02830774182
:do {
    /ip hotspot user add name="02830774182" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02830774182";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02830774182";
};

# المستخدم 104: 02142391284
:do {
    /ip hotspot user add name="02142391284" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02142391284";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02142391284";
};

# المستخدم 105: 02510113280
:do {
    /ip hotspot user add name="02510113280" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02510113280";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02510113280";
};

# المستخدم 106: 02264231798
:do {
    /ip hotspot user add name="02264231798" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02264231798";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02264231798";
};

# المستخدم 107: 02135208634
:do {
    /ip hotspot user add name="02135208634" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02135208634";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02135208634";
};

# المستخدم 108: 02393317424
:do {
    /ip hotspot user add name="02393317424" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02393317424";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02393317424";
};

# المستخدم 109: 02900559700
:do {
    /ip hotspot user add name="02900559700" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02900559700";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02900559700";
};

# المستخدم 110: 02139755197
:do {
    /ip hotspot user add name="02139755197" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02139755197";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02139755197";
};

# المستخدم 111: 02602521613
:do {
    /ip hotspot user add name="02602521613" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02602521613";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02602521613";
};

# المستخدم 112: 02670422537
:do {
    /ip hotspot user add name="02670422537" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02670422537";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02670422537";
};

# المستخدم 113: 02617382004
:do {
    /ip hotspot user add name="02617382004" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02617382004";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02617382004";
};

# المستخدم 114: 02254655519
:do {
    /ip hotspot user add name="02254655519" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02254655519";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02254655519";
};

# المستخدم 115: 02289595598
:do {
    /ip hotspot user add name="02289595598" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02289595598";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02289595598";
};

# المستخدم 116: 02685011666
:do {
    /ip hotspot user add name="02685011666" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02685011666";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02685011666";
};

# المستخدم 117: 02132025645
:do {
    /ip hotspot user add name="02132025645" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02132025645";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02132025645";
};

# المستخدم 118: 02372766160
:do {
    /ip hotspot user add name="02372766160" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02372766160";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02372766160";
};

# المستخدم 119: 02076394912
:do {
    /ip hotspot user add name="02076394912" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02076394912";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02076394912";
};

# المستخدم 120: 02182824676
:do {
    /ip hotspot user add name="02182824676" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02182824676";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02182824676";
};

# المستخدم 121: 02483539034
:do {
    /ip hotspot user add name="02483539034" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02483539034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02483539034";
};

# المستخدم 122: 02333910332
:do {
    /ip hotspot user add name="02333910332" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02333910332";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02333910332";
};

# المستخدم 123: 02781484527
:do {
    /ip hotspot user add name="02781484527" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02781484527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02781484527";
};

# المستخدم 124: 02897605116
:do {
    /ip hotspot user add name="02897605116" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02897605116";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02897605116";
};

# المستخدم 125: 02064523417
:do {
    /ip hotspot user add name="02064523417" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02064523417";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02064523417";
};

# المستخدم 126: 02958119362
:do {
    /ip hotspot user add name="02958119362" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02958119362";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02958119362";
};

# المستخدم 127: 02751550356
:do {
    /ip hotspot user add name="02751550356" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02751550356";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02751550356";
};

# المستخدم 128: 02680034385
:do {
    /ip hotspot user add name="02680034385" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02680034385";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02680034385";
};

# المستخدم 129: 02950104484
:do {
    /ip hotspot user add name="02950104484" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02950104484";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02950104484";
};

# المستخدم 130: 02979542468
:do {
    /ip hotspot user add name="02979542468" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02979542468";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02979542468";
};

# المستخدم 131: 02821182007
:do {
    /ip hotspot user add name="02821182007" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02821182007";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02821182007";
};

# المستخدم 132: 02365710503
:do {
    /ip hotspot user add name="02365710503" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02365710503";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02365710503";
};

# المستخدم 133: 02832812252
:do {
    /ip hotspot user add name="02832812252" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02832812252";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02832812252";
};

# المستخدم 134: 02733350656
:do {
    /ip hotspot user add name="02733350656" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02733350656";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02733350656";
};

# المستخدم 135: 02187152783
:do {
    /ip hotspot user add name="02187152783" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02187152783";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02187152783";
};

# المستخدم 136: 02676942172
:do {
    /ip hotspot user add name="02676942172" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02676942172";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02676942172";
};

# المستخدم 137: 02016822353
:do {
    /ip hotspot user add name="02016822353" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02016822353";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02016822353";
};

# المستخدم 138: 02404565389
:do {
    /ip hotspot user add name="02404565389" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02404565389";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02404565389";
};

# المستخدم 139: 02729706551
:do {
    /ip hotspot user add name="02729706551" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02729706551";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02729706551";
};

# المستخدم 140: 02325121457
:do {
    /ip hotspot user add name="02325121457" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02325121457";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02325121457";
};

# المستخدم 141: 02292800135
:do {
    /ip hotspot user add name="02292800135" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02292800135";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02292800135";
};

# المستخدم 142: 02066139187
:do {
    /ip hotspot user add name="02066139187" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02066139187";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02066139187";
};

# المستخدم 143: 02690058638
:do {
    /ip hotspot user add name="02690058638" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02690058638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02690058638";
};

# المستخدم 144: 02542311513
:do {
    /ip hotspot user add name="02542311513" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02542311513";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02542311513";
};

# المستخدم 145: 02339284320
:do {
    /ip hotspot user add name="02339284320" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02339284320";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02339284320";
};

# المستخدم 146: 02235595628
:do {
    /ip hotspot user add name="02235595628" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02235595628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02235595628";
};

# المستخدم 147: 02484374548
:do {
    /ip hotspot user add name="02484374548" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02484374548";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02484374548";
};

# المستخدم 148: 02275087909
:do {
    /ip hotspot user add name="02275087909" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02275087909";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02275087909";
};

# المستخدم 149: 02062297263
:do {
    /ip hotspot user add name="02062297263" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02062297263";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02062297263";
};

# المستخدم 150: 02284081878
:do {
    /ip hotspot user add name="02284081878" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02284081878";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02284081878";
};

# المستخدم 151: 02468488249
:do {
    /ip hotspot user add name="02468488249" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02468488249";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02468488249";
};

# المستخدم 152: 02171642605
:do {
    /ip hotspot user add name="02171642605" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02171642605";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02171642605";
};

# المستخدم 153: 02884006816
:do {
    /ip hotspot user add name="02884006816" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02884006816";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02884006816";
};

# المستخدم 154: 02883228542
:do {
    /ip hotspot user add name="02883228542" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02883228542";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02883228542";
};

# المستخدم 155: 02842808582
:do {
    /ip hotspot user add name="02842808582" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02842808582";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02842808582";
};

# المستخدم 156: 02629169979
:do {
    /ip hotspot user add name="02629169979" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02629169979";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02629169979";
};

# المستخدم 157: 02032348982
:do {
    /ip hotspot user add name="02032348982" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02032348982";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02032348982";
};

# المستخدم 158: 02283155593
:do {
    /ip hotspot user add name="02283155593" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02283155593";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02283155593";
};

# المستخدم 159: 02085127711
:do {
    /ip hotspot user add name="02085127711" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02085127711";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02085127711";
};

# المستخدم 160: 02391471409
:do {
    /ip hotspot user add name="02391471409" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02391471409";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02391471409";
};

# المستخدم 161: 02251411507
:do {
    /ip hotspot user add name="02251411507" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02251411507";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02251411507";
};

# المستخدم 162: 02513969523
:do {
    /ip hotspot user add name="02513969523" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02513969523";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02513969523";
};

# المستخدم 163: 02047230785
:do {
    /ip hotspot user add name="02047230785" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02047230785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02047230785";
};

# المستخدم 164: 02599906563
:do {
    /ip hotspot user add name="02599906563" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02599906563";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02599906563";
};

# المستخدم 165: 02852758122
:do {
    /ip hotspot user add name="02852758122" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02852758122";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02852758122";
};

# المستخدم 166: 02644279899
:do {
    /ip hotspot user add name="02644279899" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02644279899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02644279899";
};

# المستخدم 167: 02300885209
:do {
    /ip hotspot user add name="02300885209" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02300885209";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02300885209";
};

# المستخدم 168: 02422701020
:do {
    /ip hotspot user add name="02422701020" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02422701020";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02422701020";
};

# المستخدم 169: 02141818795
:do {
    /ip hotspot user add name="02141818795" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02141818795";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02141818795";
};

# المستخدم 170: 02730800373
:do {
    /ip hotspot user add name="02730800373" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02730800373";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02730800373";
};

# المستخدم 171: 02690670065
:do {
    /ip hotspot user add name="02690670065" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02690670065";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02690670065";
};

# المستخدم 172: 02695873110
:do {
    /ip hotspot user add name="02695873110" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02695873110";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02695873110";
};

# المستخدم 173: 02998965565
:do {
    /ip hotspot user add name="02998965565" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02998965565";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02998965565";
};

# المستخدم 174: 02091441967
:do {
    /ip hotspot user add name="02091441967" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02091441967";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02091441967";
};

# المستخدم 175: 02834862765
:do {
    /ip hotspot user add name="02834862765" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02834862765";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02834862765";
};

# المستخدم 176: 02983082872
:do {
    /ip hotspot user add name="02983082872" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02983082872";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02983082872";
};

# المستخدم 177: 02009239048
:do {
    /ip hotspot user add name="02009239048" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02009239048";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02009239048";
};

# المستخدم 178: 02651462742
:do {
    /ip hotspot user add name="02651462742" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02651462742";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02651462742";
};

# المستخدم 179: 02739798932
:do {
    /ip hotspot user add name="02739798932" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02739798932";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02739798932";
};

# المستخدم 180: 02044110374
:do {
    /ip hotspot user add name="02044110374" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02044110374";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02044110374";
};

# المستخدم 181: 02019346265
:do {
    /ip hotspot user add name="02019346265" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02019346265";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02019346265";
};

# المستخدم 182: 02897279743
:do {
    /ip hotspot user add name="02897279743" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02897279743";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02897279743";
};

# المستخدم 183: 02750081091
:do {
    /ip hotspot user add name="02750081091" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02750081091";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02750081091";
};

# المستخدم 184: 02529782256
:do {
    /ip hotspot user add name="02529782256" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02529782256";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02529782256";
};

# المستخدم 185: 02267389435
:do {
    /ip hotspot user add name="02267389435" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02267389435";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02267389435";
};

# المستخدم 186: 02224078036
:do {
    /ip hotspot user add name="02224078036" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02224078036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02224078036";
};

# المستخدم 187: 02030395192
:do {
    /ip hotspot user add name="02030395192" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02030395192";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02030395192";
};

# المستخدم 188: 02041002300
:do {
    /ip hotspot user add name="02041002300" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02041002300";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02041002300";
};

# المستخدم 189: 02774732988
:do {
    /ip hotspot user add name="02774732988" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02774732988";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02774732988";
};

# المستخدم 190: 02752903698
:do {
    /ip hotspot user add name="02752903698" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02752903698";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02752903698";
};

# المستخدم 191: 02678027404
:do {
    /ip hotspot user add name="02678027404" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02678027404";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02678027404";
};

# المستخدم 192: 02449966429
:do {
    /ip hotspot user add name="02449966429" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02449966429";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02449966429";
};

# المستخدم 193: 02819473470
:do {
    /ip hotspot user add name="02819473470" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02819473470";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02819473470";
};

# المستخدم 194: 02679598625
:do {
    /ip hotspot user add name="02679598625" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02679598625";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02679598625";
};

# المستخدم 195: 02659295491
:do {
    /ip hotspot user add name="02659295491" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02659295491";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02659295491";
};

# المستخدم 196: 02483447219
:do {
    /ip hotspot user add name="02483447219" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02483447219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02483447219";
};

# المستخدم 197: 02216897972
:do {
    /ip hotspot user add name="02216897972" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02216897972";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02216897972";
};

# المستخدم 198: 02747048785
:do {
    /ip hotspot user add name="02747048785" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02747048785";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02747048785";
};

# المستخدم 199: 02882819596
:do {
    /ip hotspot user add name="02882819596" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02882819596";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02882819596";
};

# المستخدم 200: 02454374620
:do {
    /ip hotspot user add name="02454374620" password="" profile="CARD" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02454374620";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02454374620";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
