# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-15 01:50:49
# القالب: 10
# النظام: hotspot
# عدد الكروت: 1
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 1";

:local success 0;
:local errors 0;
:local total 1;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 1 مستخدم Hotspot...";

# المستخدم 1: 02681363517
:do {
    /ip hotspot user add name="02681363517" password="" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02681363517";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02681363517";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
