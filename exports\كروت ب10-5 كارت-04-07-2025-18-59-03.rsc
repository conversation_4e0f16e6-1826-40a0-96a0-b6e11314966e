# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 18:59:03
# القالب: 10
# النظام: hotspot
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 5 مستخدم Hotspot...";

# المستخدم 1: 1060907713
:do {
    /ip hotspot user add name="1060907713" password="4015" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1060907713";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1060907713";
};

# المستخدم 2: 1040256942
:do {
    /ip hotspot user add name="1040256942" password="6544" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1040256942";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1040256942";
};

# المستخدم 3: 1078984996
:do {
    /ip hotspot user add name="1078984996" password="8664" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1078984996";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1078984996";
};

# المستخدم 4: 1080831466
:do {
    /ip hotspot user add name="1080831466" password="6199" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1080831466";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1080831466";
};

# المستخدم 5: 1027567632
:do {
    /ip hotspot user add name="1027567632" password="2805" profile="CARD" limit-bytes-total=3221225472 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 1027567632";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 1027567632";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
