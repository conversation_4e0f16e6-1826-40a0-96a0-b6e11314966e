# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 18:57:45
# القالب: 20
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 20";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 02682926812
:do {
    /tool user-manager user add customer="admin" username="02682926812" password="" profile="20";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02682926812";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02682926812";
};

# المستخدم 2: 02956516652
:do {
    /tool user-manager user add customer="admin" username="02956516652" password="" profile="20";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02956516652";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02956516652";
};

# المستخدم 3: 02763833266
:do {
    /tool user-manager user add customer="admin" username="02763833266" password="" profile="20";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02763833266";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02763833266";
};

# المستخدم 4: 02487407298
:do {
    /tool user-manager user add customer="admin" username="02487407298" password="" profile="20";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02487407298";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02487407298";
};

# المستخدم 5: 02736194359
:do {
    /tool user-manager user add customer="admin" username="02736194359" password="" profile="20";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 02736194359";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 02736194359";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 20";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
