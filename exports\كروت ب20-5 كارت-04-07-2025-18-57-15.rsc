# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 18:57:15
# القالب: 10
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 2032321427
:do {
    /tool user-manager user add customer="admin" username="2032321427" password="94254275" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032321427";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032321427";
};

# المستخدم 2: 2098391261
:do {
    /tool user-manager user add customer="admin" username="2098391261" password="37179634" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2098391261";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2098391261";
};

# المستخدم 3: 2032751702
:do {
    /tool user-manager user add customer="admin" username="2032751702" password="67000819" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2032751702";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2032751702";
};

# المستخدم 4: 2037676900
:do {
    /tool user-manager user add customer="admin" username="2037676900" password="83321010" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037676900";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037676900";
};

# المستخدم 5: 2024219056
:do {
    /tool user-manager user add customer="admin" username="2024219056" password="49237568" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024219056";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024219056";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
