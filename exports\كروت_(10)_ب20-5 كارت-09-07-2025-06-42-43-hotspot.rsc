# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-09 06:42:43
# القالب: 10
# النظام: hotspot
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 5 مستخدم Hotspot...";

# المستخدم 1: 2059764902
:do {
    /ip hotspot user add name="2059764902" password="40720953" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2059764902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2059764902";
};

# المستخدم 2: 2058769023
:do {
    /ip hotspot user add name="2058769023" password="39172519" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2058769023";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2058769023";
};

# المستخدم 3: 2050992806
:do {
    /ip hotspot user add name="2050992806" password="41278558" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050992806";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050992806";
};

# المستخدم 4: 2037415587
:do {
    /ip hotspot user add name="2037415587" password="65782781" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037415587";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037415587";
};

# المستخدم 5: 2050397475
:do {
    /ip hotspot user add name="2050397475" password="90626468" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2050397475";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2050397475";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
