# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 18:49:46
# القالب: 10
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 2031396933
:do {
    /tool user-manager user add customer="admin" username="2031396933" password="74710400" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031396933";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031396933";
};

# المستخدم 2: 2089812809
:do {
    /tool user-manager user add customer="admin" username="2089812809" password="92358276" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2089812809";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2089812809";
};

# المستخدم 3: 2037367784
:do {
    /tool user-manager user add customer="admin" username="2037367784" password="86163435" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037367784";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037367784";
};

# المستخدم 4: 2031086760
:do {
    /tool user-manager user add customer="admin" username="2031086760" password="16232271" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2031086760";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2031086760";
};

# المستخدم 5: 2037353647
:do {
    /tool user-manager user add customer="admin" username="2037353647" password="05971648" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2037353647";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2037353647";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
