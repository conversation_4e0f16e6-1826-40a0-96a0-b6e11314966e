# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-09 07:59:16
# القالب: 20
# النظام: hotspot
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 20";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 5 مستخدم Hotspot...";

# المستخدم 1: 0283582884
:do {
    /ip hotspot user add name="0283582884" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0283582884";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0283582884";
};

# المستخدم 2: 0247547366
:do {
    /ip hotspot user add name="0247547366" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0247547366";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0247547366";
};

# المستخدم 3: 0218651478
:do {
    /ip hotspot user add name="0218651478" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0218651478";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0218651478";
};

# المستخدم 4: 0217346775
:do {
    /ip hotspot user add name="0217346775" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0217346775";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0217346775";
};

# المستخدم 5: 0228950357
:do {
    /ip hotspot user add name="0228950357" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0228950357";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0228950357";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 20";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
