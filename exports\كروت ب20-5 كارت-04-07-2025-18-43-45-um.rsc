# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 18:43:46
# القالب: 10
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 2024715797
:do {
    /tool user-manager user add customer="admin" username="2024715797" password="43017083" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2024715797";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2024715797";
};

# المستخدم 2: 2069998461
:do {
    /tool user-manager user add customer="admin" username="2069998461" password="75981097" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2069998461";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2069998461";
};

# المستخدم 3: 2006436815
:do {
    /tool user-manager user add customer="admin" username="2006436815" password="19780626" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006436815";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006436815";
};

# المستخدم 4: 2005012292
:do {
    /tool user-manager user add customer="admin" username="2005012292" password="27652791" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2005012292";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2005012292";
};

# المستخدم 5: 2043675036
:do {
    /tool user-manager user add customer="admin" username="2043675036" password="42064687" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2043675036";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2043675036";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
