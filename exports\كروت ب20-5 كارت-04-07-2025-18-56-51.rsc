# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-04 18:56:51
# القالب: 10
# النظام: user_manager
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: user_manager";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي User Manager
:put "👤 إضافة 5 مستخدم User Manager...";

# المستخدم 1: 2009758038
:do {
    /tool user-manager user add customer="admin" username="2009758038" password="37539300" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2009758038";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2009758038";
};

# المستخدم 2: 2029662650
:do {
    /tool user-manager user add customer="admin" username="2029662650" password="77531296" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2029662650";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2029662650";
};

# المستخدم 3: 2082761219
:do {
    /tool user-manager user add customer="admin" username="2082761219" password="81528112" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2082761219";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2082761219";
};

# المستخدم 4: 2001999527
:do {
    /tool user-manager user add customer="admin" username="2001999527" password="25752936" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2001999527";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2001999527";
};

# المستخدم 5: 2086617994
:do {
    /tool user-manager user add customer="admin" username="2086617994" password="84586244" profile="10";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2086617994";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2086617994";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
