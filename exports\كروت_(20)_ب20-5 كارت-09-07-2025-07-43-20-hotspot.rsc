# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-09 07:43:20
# القالب: 20
# النظام: hotspot
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 20";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 5 مستخدم Hotspot...";

# المستخدم 1: 0208650675
:do {
    /ip hotspot user add name="0208650675" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0208650675";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0208650675";
};

# المستخدم 2: 0216459780
:do {
    /ip hotspot user add name="0216459780" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0216459780";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0216459780";
};

# المستخدم 3: 0261155899
:do {
    /ip hotspot user add name="0261155899" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0261155899";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0261155899";
};

# المستخدم 4: 0272953302
:do {
    /ip hotspot user add name="0272953302" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0272953302";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0272953302";
};

# المستخدم 5: 0277397619
:do {
    /ip hotspot user add name="0277397619" password="" profile="CARDALLLLLLL1" limit-bytes-total=5997854720 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 0277397619";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 0277397619";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 20";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
