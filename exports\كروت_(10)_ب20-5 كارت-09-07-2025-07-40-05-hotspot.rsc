# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-09 07:40:05
# القالب: 10
# النظام: hotspot
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 5 مستخدم Hotspot...";

# المستخدم 1: 2006332626
:do {
    /ip hotspot user add name="2006332626" password="56444885" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2006332626";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2006332626";
};

# المستخدم 2: 2028294405
:do {
    /ip hotspot user add name="2028294405" password="14746839" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2028294405";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2028294405";
};

# المستخدم 3: 2046160638
:do {
    /ip hotspot user add name="2046160638" password="60485494" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2046160638";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2046160638";
};

# المستخدم 4: 2071605902
:do {
    /ip hotspot user add name="2071605902" password="83316548" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2071605902";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2071605902";
};

# المستخدم 5: 2090986687
:do {
    /ip hotspot user add name="2090986687" password="33723721" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2090986687";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2090986687";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
