# MikroTik Script - نسخة احتياطية للكروت
# تم الإنشاء: 2025-07-09 07:42:53
# القالب: 10
# النظام: hotspot
# عدد الكروت: 5
# تم إنشاؤه بواسطة: مولد كروت MikroTik

:put "🚀 بدء تنفيذ النسخة الاحتياطية للكروت";
:put "📋 القالب: 10";
:put "🎯 النظام: hotspot";
:put "📊 عدد الكروت: 5";

:local success 0;
:local errors 0;
:local total 5;

:put "⏳ بدء إضافة الكروت...";

# إضافة مستخدمي Hotspot
:put "🌐 إضافة 5 مستخدم Hotspot...";

# المستخدم 1: 2068840628
:do {
    /ip hotspot user add name="2068840628" password="25432914" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2068840628";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2068840628";
};

# المستخدم 2: 2000771033
:do {
    /ip hotspot user add name="2000771033" password="04538556" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2000771033";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2000771033";
};

# المستخدم 3: 2087880640
:do {
    /ip hotspot user add name="2087880640" password="41342206" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2087880640";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2087880640";
};

# المستخدم 4: 2075238147
:do {
    /ip hotspot user add name="2075238147" password="29958542" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2075238147";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2075238147";
};

# المستخدم 5: 2008651034
:do {
    /ip hotspot user add name="2008651034" password="44924114" profile="CARDALLLLLLL1" limit-bytes-total=6442450944 email="<EMAIL>";
    :set success ($success + 1);
    :put "✅ تم إضافة المستخدم: 2008651034";
} on-error={
    :set errors ($errors + 1);
    :put "❌ خطأ في إضافة المستخدم: 2008651034";
};


:put "📊 تم الانتهاء من النسخة الاحتياطية";
:put "✅ نجح: $success كرت";
:put "❌ فشل: $errors كرت";
:put "📈 الإجمالي: $total كرت";

:put "🎉 تم استعادة جميع كروت القالب: 10";
:put "💡 النسخة الاحتياطية مكتملة بنجاح!";
